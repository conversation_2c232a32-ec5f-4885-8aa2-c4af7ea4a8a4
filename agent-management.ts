// src/store/auth.ts
import { create } from 'zustand';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { User } from '@/types';

interface AuthState {
  user: User | null;
  loading: boolean;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: true,
  setUser: (user) => set({ user }),
  setLoading: (loading) => set({ loading }),
  signOut: async () => {
    const supabase = createClientComponentClient();
    await supabase.auth.signOut();
    set({ user: null });
  },
  refreshUser: async () => {
    try {
      const response = await fetch('/api/user/profile');
      const data = await response.json();
      if (data.profile) {
        set({ user: data.profile });
      }
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  },
}));

// ===================================

// src/store/agents.ts
import { create } from 'zustand';
import { AIAgent } from '@/types';

interface AgentsState {
  agents: AIAgent[];
  selectedAgent: AIAgent | null;
  loading: boolean;
  setAgents: (agents: AIAgent[]) => void;
  setSelectedAgent: (agent: AIAgent | null) => void;
  addAgent: (agent: AIAgent) => void;
  updateAgent: (id: string, updates: Partial<AIAgent>) => void;
  removeAgent: (id: string) => void;
  setLoading: (loading: boolean) => void;
}

export const useAgentsStore = create<AgentsState>((set, get) => ({
  agents: [],
  selectedAgent: null,
  loading: false,
  setAgents: (agents) => set({ agents }),
  setSelectedAgent: (agent) => set({ selectedAgent: agent }),
  addAgent: (agent) => set({ agents: [...get().agents, agent] }),
  updateAgent: (id, updates) => set({
    agents: get().agents.map(agent => 
      agent.id === id ? { ...agent, ...updates } : agent
    )
  }),
  removeAgent: (id) => set({
    agents: get().agents.filter(agent => agent.id !== id)
  }),
  setLoading: (loading) => set({ loading }),
}));

// ===================================

// src/hooks/use-agents.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AIAgent } from '@/types';
import { useAgentsStore } from '@/store/agents';
import toast from 'react-hot-toast';

export function useAgents() {
  const { setAgents, setLoading } = useAgentsStore();

  return useQuery({
    queryKey: ['agents'],
    queryFn: async (): Promise<AIAgent[]> => {
      setLoading(true);
      const response = await fetch('/api/agents');
      if (!response.ok) {
        throw new Error('Failed to fetch agents');
      }
      const data = await response.json();
      setAgents(data.agents || []);
      setLoading(false);
      return data.agents || [];
    },
  });
}

export function useCreateAgent() {
  const queryClient = useQueryClient();
  const { addAgent } = useAgentsStore();

  return useMutation({
    mutationFn: async (agentData: Omit<AIAgent, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
      const response = await fetch('/api/agents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(agentData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create agent');
      }

      return response.json();
    },
    onSuccess: (data) => {
      addAgent(data.agent);
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent created successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateAgent() {
  const queryClient = useQueryClient();
  const { updateAgent } = useAgentsStore();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<AIAgent> }) => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update agent');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      updateAgent(variables.id, data.agent);
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent updated successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useDeleteAgent() {
  const queryClient = useQueryClient();
  const { removeAgent } = useAgentsStore();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/agents/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete agent');
      }

      return response.json();
    },
    onSuccess: (_, id) => {
      removeAgent(id);
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent deleted successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useExecuteAgent() {
  return useMutation({
    mutationFn: async ({ agentId, input }: { agentId: string; input: string }) => {
      const response = await fetch(`/api/agents/${agentId}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to execute agent');
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success('Agent executed successfully!');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// ===================================

// src/components/agent/framework-selector.tsx
'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Framework } from '@/types';

interface FrameworkSelectorProps {
  frameworks: Framework[];
  selectedFramework: string;
  onSelectFramework: (frameworkId: string) => void;
}

export function FrameworkSelector({ 
  frameworks, 
  selectedFramework, 
  onSelectFramework 
}: FrameworkSelectorProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {frameworks.map((framework, index) => (
        <motion.div
          key={framework.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card 
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedFramework === framework.id 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:shadow-md'
            }`}
            onClick={() => onSelectFramework(framework.id)}
          >
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-3">{framework.icon}</div>
              <h3 className="font-bold text-lg mb-2">{framework.name}</h3>
              <p className="text-sm text-gray-600 mb-3">{framework.description}</p>
              <div className="flex items-center justify-center space-x-1 text-xs">
                <span className={`px-2 py-1 rounded-full ${
                  framework.complexity === 'beginner' ? 'bg-green-100 text-green-800' :
                  framework.complexity === 'intermediate' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {framework.complexity}
                </span>
                {framework.popular && (
                  <span className="px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                    Popular
                  </span>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}

// ===================================

// src/components/agent/model-selector.tsx
'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Model } from '@/types';
import { formatCurrency } from '@/lib/utils';

interface ModelSelectorProps {
  models: Model[];
  selectedModel: string;
  onSelectModel: (modelId: string) => void;
}

export function ModelSelector({ models, selectedModel, onSelectModel }: ModelSelectorProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {models.map((model, index) => (
        <motion.div
          key={model.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card 
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedModel === model.id 
                ? 'ring-2 ring-blue-500 bg-blue-50' 
                : 'hover:shadow-md'
            }`}
            onClick={() => onSelectModel(model.id)}
          >
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-bold text-lg">{model.name}</h3>
                {model.recommended && (
                  <span className="px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium">
                    Recommended
                  </span>
                )}
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Speed:</span>
                  <span className="font-medium">{model.speed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cost:</span>
                  <span className="font-medium">{formatCurrency(model.costPer1kTokens)}/1K tokens</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Max Tokens:</span>
                  <span className="font-medium">{model.maxTokens.toLocaleString()}</span>
                </div>
              </div>

              <div className="mt-4">
                <div className="text-xs text-gray-500 mb-2">Capabilities:</div>
                <div className="flex flex-wrap gap-1">
                  {model.capabilities.slice(0, 3).map((capability) => (
                    <span 
                      key={capability}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                    >
                      {capability}
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}

// ===================================

// src/components/agent/agent-form.tsx
'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FrameworkSelector } from './framework-selector';
import { ModelSelector } from './model-selector';
import { useCreateAgent } from '@/hooks/use-agents';
import { CEREBRAS_MODELS } from '@/lib/cerebras';
import { Framework } from '@/types';

const agentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  framework: z.string().min(1, 'Framework is required'),
  model: z.string().min(1, 'Model is required'),
  systemPrompt: z.string().min(1, 'System prompt is required'),
  temperature: z.number().min(0).max(2),
  maxTokens: z.number().min(1).max(4096),
});

type AgentFormData = z.infer<typeof agentSchema>;

const FRAMEWORKS: Framework[] = [
  {
    id: 'langchain',
    name: 'LangChain',
    description: 'Most popular framework',
    icon: '🦜',
    features: ['Chain composition', 'Memory management', 'Tool integration'],
    complexity: 'beginner',
    popular: true,
  },
  {
    id: 'autogen',
    name: 'AutoGen',
    description: 'Multi-agent conversations',
    icon: '🤖',
    features: ['Multi-agent systems', 'Role-based agents', 'Group chat'],
    complexity: 'intermediate',
    popular: false,
  },
  {
    id: 'semantic-kernel',
    name: 'Semantic Kernel',
    description: 'Microsoft AI framework',
    icon: '⚡',
    features: ['Plugin architecture', 'Planning', 'Skills'],
    complexity: 'intermediate',
    popular: false,
  },
  {
    id: 'llama-index',
    name: 'LlamaIndex',
    description: 'Data framework for LLMs',
    icon: '🦙',
    features: ['Data ingestion', 'Indexing', 'Query interface'],
    complexity: 'advanced',
    popular: false,
  },
];

interface AgentFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function AgentForm({ onSuccess, onCancel }: AgentFormProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const createAgent = useCreateAgent();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<AgentFormData>({
    resolver: zodResolver(agentSchema),
    defaultValues: {
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: 'You are a helpful AI assistant.',
    },
  });

  const selectedFramework = watch('framework');
  const selectedModel = watch('model');

  const steps = [
    'Framework',
    'Model',
    'Configuration',
    'Review'
  ];

  const onSubmit = async (data: AgentFormData) => {
    const agentData = {
      name: data.name,
      description: data.description,
      framework: data.framework as any,
      model: data.model,
      configuration: {
        systemPrompt: data.systemPrompt,
        temperature: data.temperature,
        maxTokens: data.maxTokens,
      },
      status: 'idle' as const,
      is_active: true,
    };

    createAgent.mutate(agentData, {
      onSuccess: () => {
        onSuccess?.();
      },
    });
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div
              key={step}
              className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  index <= currentStep
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
              <span
                className={`ml-2 text-sm ${
                  index <= currentStep ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                {step}
              </span>
              {index < steps.length - 1 && (
                <div
                  className={`flex-1 h-px mx-4 ${
                    index < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {currentStep === 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Choose AI Framework</CardTitle>
              </CardHeader>
              <CardContent>
                <FrameworkSelector
                  frameworks={FRAMEWORKS}
                  selectedFramework={selectedFramework}
                  onSelectFramework={(id) => setValue('framework', id)}
                />
                {errors.framework && (
                  <p className="text-red-500 text-sm mt-2">{errors.framework.message}</p>
                )}
              </CardContent>
            </Card>
          )}

          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>Select Model</CardTitle>
              </CardHeader>
              <CardContent>
                <ModelSelector
                  models={CEREBRAS_MODELS}
                  selectedModel={selectedModel}
                  onSelectModel={(id) => setValue('model', id)}
                />
                {errors.model && (
                  <p className="text-red-500 text-sm mt-2">{errors.model.message}</p>
                )}
              </CardContent>
            </Card>
          )}

          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>Configure Agent</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Agent Name</label>
                  <Input
                    {...register('name')}
                    placeholder="My AI Assistant"
                  />
                  {errors.name && (
                    <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Description (Optional)</label>
                  <Textarea
                    {...register('description')}
                    placeholder="Describe what this agent does..."
                    rows={3}
                  />
                  {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">System Prompt</label>
                  <Textarea
                    {...register('systemPrompt')}
                    placeholder="You are a helpful AI assistant that..."
                    rows={4}
                  />
                  {errors.systemPrompt && (
                    <p className="text-red-500 text-sm mt-1">{errors.systemPrompt.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Temperature</label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0"
                      max="2"
                      {...register('temperature', { valueAsNumber: true })}
                    />
                    {errors.temperature && (
                      <p className="text-red-500 text-sm mt-1">{errors.temperature.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Max Tokens</label>
                    <Input
                      type="number"
                      min="1"
                      max="4096"
                      {...register('maxTokens', { valueAsNumber: true })}
                    />
                    {errors.maxTokens && (
                      <p className="text-red-500 text-sm mt-1">{errors.maxTokens.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle>Review Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Name</div>
                    <div className="font-medium">{watch('name')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Framework</div>
                    <div className="font-medium capitalize">{watch('framework')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Model</div>
                    <div className="font-medium">{watch('model')}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Temperature</div>
                    <div className="font-medium">{watch('temperature')}</div>
                  </div>
                </div>
                
                {watch('description') && (
                  <div>
                    <div className="text-sm text-gray-600">Description</div>
                    <div className="font-medium">{watch('description')}</div>
                  </div>
                )}

                <div>
                  <div className="text-sm text-gray-600">System Prompt</div>
                  <div className="font-medium bg-gray-50 p-3 rounded-lg text-sm">
                    {watch('systemPrompt')}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </motion.div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <div>
            {currentStep > 0 && (
              <Button type="button" variant="outline" onClick={handlePrevious}>
                Previous
              </Button>
            )}
            {onCancel && (
              <Button type="button" variant="ghost" onClick={onCancel} className="ml-2">
                Cancel
              </Button>
            )}
          </div>

          <div>
            {currentStep < steps.length - 1 ? (
              <Button 
                type="button" 
                onClick={handleNext}
                disabled={
                  (currentStep === 0 && !selectedFramework) ||
                  (currentStep === 1 && !selectedModel)
                }
              >
                Next
              </Button>
            ) : (
              <Button 
                type="submit" 
                disabled={createAgent.isPending}
              >
                {createAgent.isPending ? 'Creating...' : 'Create Agent'}
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}