// src/app/layout.tsx
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'AI Orchestration Platform',
  description: 'Build AI agents at lightning speed with multi-framework support and enterprise-grade infrastructure.',
  keywords: ['AI', 'agents', 'orchestration', 'cerebras', 'langchain', 'autogen'],
  authors: [{ name: 'AI Orchestration Platform' }],
  openGraph: {
    title: 'AI Orchestration Platform',
    description: 'Build AI agents at lightning speed',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}

// ===================================

// src/app/globals.css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

/* Loading spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Code syntax highlighting */
.code-block {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
}

.code-block pre {
  @apply m-0;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ===================================

// src/components/providers.tsx
'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000,
            retry: (failureCount, error: any) => {
              if (error?.status === 401 || error?.status === 403) {
                return false;
              }
              return failureCount < 3;
            },
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            style: {
              background: '#059669',
            },
          },
          error: {
            style: {
              background: '#DC2626',
            },
          },
        }}
      />
    </QueryClientProvider>
  );
}

// ===================================

// src/components/agent/agent-executor.tsx
'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  Square, 
  Copy, 
  Download, 
  Clock,
  Zap,
  DollarSign,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useExecuteAgent } from '@/hooks/use-agents';
import { AIAgent } from '@/types';
import { formatCurrency, formatDuration, formatTokens } from '@/lib/utils';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import toast from 'react-hot-toast';

interface AgentExecutorProps {
  agent: AIAgent;
}

interface ExecutionResult {
  output: string;
  tokens_used: number;
  execution_time_ms: number;
  cost_usd: number;
  execution_id: string;
}

export function AgentExecutor({ agent }: AgentExecutorProps) {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<ExecutionResult[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  const executeAgent = useExecuteAgent();

  const handleExecute = async () => {
    if (!input.trim()) {
      toast.error('Please enter some input');
      return;
    }

    setIsExecuting(true);
    
    executeAgent.mutate(
      { agentId: agent.id, input: input.trim() },
      {
        onSuccess: (result) => {
          setResults(prev => [result, ...prev]);
          setInput('');
        },
        onError: (error) => {
          console.error('Execution error:', error);
        },
        onSettled: () => {
          setIsExecuting(false);
        },
      }
    );
  };

  const handleStop = () => {
    setIsExecuting(false);
    // In a real implementation, you would call an API to stop the execution
    toast.success('Execution stopped');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const downloadResult = (result: ExecutionResult, index: number) => {
    const blob = new Blob([result.output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agent-result-${index + 1}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Agent Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              agent.status === 'running' ? 'bg-green-500' :
              agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
            }`} />
            {agent.name}
          </CardTitle>
          <div className="text-sm text-gray-600">
            Framework: {agent.framework} • Model: {agent.model}
          </div>
        </CardHeader>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle>Input</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your prompt or question here..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            rows={4}
            disabled={isExecuting}
          />
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {input.length} characters
            </div>
            
            <div className="flex space-x-2">
              {isExecuting ? (
                <Button 
                  variant="destructive" 
                  onClick={handleStop}
                  disabled={!isExecuting}
                >
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              ) : (
                <Button 
                  onClick={handleExecute}
                  disabled={!input.trim() || isExecuting}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Execute
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Execution Status */}
      <AnimatePresence>
        {isExecuting && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="spinner" />
                  <div>
                    <div className="font-medium text-blue-900">Executing...</div>
                    <div className="text-sm text-blue-700">
                      Running {agent.name} with {agent.model}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Results</h3>
          
          {results.map((result, index) => (
            <motion.div
              key={result.execution_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      Result #{results.length - index}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(result.output)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadResult(result, index)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Clock className="w-4 h-4 mr-1" />
                      {formatDuration(result.execution_time_ms)}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Zap className="w-4 h-4 mr-1" />
                      {formatTokens(result.tokens_used)} tokens
                    </div>
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {formatCurrency(result.cost_usd)}
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="prose max-w-none">
                    {result.output.includes('```') ? (
                      <SyntaxHighlighter
                        language="text"
                        style={oneDark}
                        className="rounded-lg"
                      >
                        {result.output}
                      </SyntaxHighlighter>
                    ) : (
                      <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg">
                        {result.output}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {results.length === 0 && !isExecuting && (
        <Card>
          <CardContent className="text-center py-12">
            <Play className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to execute</h3>
            <p className="text-gray-600">
              Enter your input above and click Execute to run the agent
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// ===================================

// src/app/agents/page.tsx
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Navbar } from '@/components/layout/navbar';
import { Loading } from '@/components/layout/loading';
import { AgentForm } from '@/components/agent/agent-form';
import { AgentExecutor } from '@/components/agent/agent-executor';
import { useAgents } from '@/hooks/use-agents';
import { useAuthStore } from '@/store/auth';
import { useAgentsStore } from '@/store/agents';
import { 
  Plus, 
  Settings, 
  Play, 
  MoreVertical, 
  Edit,
  Trash2,
  Brain
} from 'lucide-react';
import { timeAgo } from '@/lib/utils';

export default function AgentsPage() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const { user } = useAuthStore();
  const { agents, selectedAgent, setSelectedAgent } = useAgentsStore();
  
  const { data: agentsData, isLoading } = useAgents();

  if (isLoading) {
    return <Loading />;
  }

  const selectedAgentData = selectedAgentId 
    ? agents.find(a => a.id === selectedAgentId) 
    : null;

  if (showCreateForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <Button 
              variant="outline" 
              onClick={() => setShowCreateForm(false)}
            >
              ← Back to Agents
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Create New Agent</h1>
          <AgentForm 
            onSuccess={() => setShowCreateForm(false)}
            onCancel={() => setShowCreateForm(false)}
          />
        </main>
      </div>
    );
  }

  if (selectedAgentData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-6">
            <Button 
              variant="outline" 
              onClick={() => setSelectedAgentId(null)}
            >
              ← Back to Agents
            </Button>
          </div>
          <AgentExecutor agent={selectedAgentData} />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar user={user} />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-900">AI Agents</h1>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Agent
          </Button>
        </div>

        {agents.length === 0 ? (
          <Card>
            <CardContent className="text-center py-16">
              <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No agents yet</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Create your first AI agent to start building intelligent applications with 
                our multi-framework platform.
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Agent
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {agents.map((agent, index) => (
              <motion.div
                key={agent.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="truncate">{agent.name}</CardTitle>
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${
                          agent.status === 'running' ? 'bg-green-500' :
                          agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                        }`} />
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    {agent.description && (
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {agent.description}
                      </p>
                    )}
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Framework:</span>
                        <span className="font-medium capitalize">{agent.framework}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Model:</span>
                        <span className="font-medium">{agent.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Created:</span>
                        <span className="font-medium">{timeAgo(agent.created_at)}</span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-6">
                      <Button 
                        size="sm" 
                        className="flex-1"
                        onClick={() => setSelectedAgentId(agent.id)}
                      >
                        <Play className="w-4 h-4 mr-1" />
                        Execute
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </main>
    </div>
  );
}

// ===================================

// README.md (Final Documentation)
# 🚀 AI Orchestration Platform

## Enterprise-Ready SaaS for AI Developers

Transform your AI development workflow with our comprehensive platform featuring multi-framework support, ultra-fast inference, and enterprise-grade monitoring.

### ⚡ Key Features

- **Multi-Framework Support**: LangChain, AutoGen, Semantic Kernel, and LlamaIndex
- **Cerebras AI Integration**: Ultra-fast inference at 450-1800 tokens/second
- **Real-Time Monitoring**: Comprehensive analytics and performance tracking
- **Enterprise Security**: Built with SOC 2 compliance and end-to-end encryption
- **Scalable Infrastructure**: Auto-scaling from prototypes to production workloads
- **Developer-First APIs**: RESTful APIs, webhooks, and SDKs

### 🎯 Perfect For

- **AI Developers**: Build and deploy agents 10x faster
- **Startups**: Rapid prototyping to production scaling
- **Enterprises**: Mission-critical AI applications with SLA guarantees
- **Research Teams**: Experiment with cutting-edge AI frameworks

## 🚀 Quick Start

### Prerequisites

- Node.js 18.17 or later
- Supabase account (free tier available)
- Cerebras AI API key
- Stripe account for payments

### 1. Clone and Install

```bash
git clone https://github.com/your-username/ai-orchestration-platform
cd ai-orchestration-platform
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env.local
```

Update `.env.local` with your API keys:

```env
# Cerebras AI
CEREBRAS_API_KEY=your_cerebras_api_key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe
STRIPE_SECRET_KEY=sk_test_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# App
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_SECRET=your_random_secret
```

### 3. Database Setup

1. Create a new Supabase project
2. Run the SQL schema from `supabase_schema.sql` in your Supabase SQL editor
3. Enable Row Level Security on all tables

### 4. Run Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to see your platform!

## 💰 Monetization Strategy

### Subscription Plans

- **Free**: 5 agents, 1K tokens/month
- **Starter ($29/mo)**: 25 agents, 10K tokens/month
- **Pro ($99/mo)**: 100 agents, 100K tokens/month
- **Enterprise ($499/mo)**: Unlimited + white-label

### Revenue Projections

- **Year 1**: $50K+ (100 paying customers)
- **Year 2**: $250K+ (500 paying customers)
- **Year 3**: $500K+ (1000+ paying customers)

## 🏗️ Architecture

### Frontend
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + Framer Motion
- **State**: Zustand + TanStack Query
- **UI**: Shadcn/ui components

### Backend
- **API**: Next.js API routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Payments**: Stripe
- **AI**: Cerebras API

### Infrastructure
- **Hosting**: Vercel (recommended)
- **Database**: Supabase Cloud
- **CDN**: Vercel Edge Network
- **Monitoring**: Built-in analytics

## 📦 Deployment

### Vercel (Recommended)

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/ai-orchestration-platform)

1. Connect your GitHub repository
2. Add environment variables
3. Deploy!

### Manual Deployment

```bash
npm run build
npm start
```

## 📈 Business Development

### Launch Strategy

1. **Beta Testing**: Recruit 50 AI developers
2. **Product Hunt**: Launch for maximum visibility
3. **Content Marketing**: AI development tutorials and guides
4. **Community Building**: Discord server and workshops

### Growth Tactics

- **Freemium Model**: Generous free tier to drive adoption
- **Developer Relations**: Open source contributions and partnerships
- **Enterprise Sales**: Direct outreach to AI teams at large companies
- **Referral Program**: User-driven growth incentives

## 🛠️ Contributing

This platform is designed as a complete business solution. Fork the repository to create your own AI orchestration platform or contribute improvements back to the community.

## 📄 License

MIT License - build your AI empire! 🚀

---

**Ready to revolutionize AI development?** 

Get started today and join the next generation of AI builders creating the future with intelligent agents.