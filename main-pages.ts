// src/app/page.tsx
'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Brain, 
  Zap, 
  Shield, 
  BarChart3, 
  Code, 
  Rocket,
  CheckCircle,
  ArrowRight,
  Star
} from 'lucide-react';

export default function HomePage() {
  const features = [
    {
      icon: Brain,
      title: 'Multi-Framework Support',
      description: 'Work with LangChain, AutoGen, Semantic Kernel, and LlamaIndex in one platform.'
    },
    {
      icon: Zap,
      title: 'Ultra-Fast Inference',
      description: 'Powered by Cerebras AI - get responses at 450-1800 tokens per second.'
    },
    {
      icon: BarChart3,
      title: 'Real-Time Monitoring',
      description: 'Track performance, costs, and usage with comprehensive analytics.'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Built with enterprise-grade security and compliance standards.'
    },
    {
      icon: Code,
      title: 'Developer-First',
      description: 'RESTful APIs, webhooks, and SDKs for seamless integration.'
    },
    {
      icon: Rocket,
      title: 'Scale Effortlessly',
      description: 'From prototype to production with automatic scaling and optimization.'
    }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      role: 'AI Engineer at TechCorp',
      content: 'This platform reduced our AI development time by 70%. The Cerebras integration is game-changing.',
      rating: 5
    },
    {
      name: 'Marcus Rodriguez',
      role: 'CTO at StartupXYZ',
      content: 'Finally, a platform that handles all our AI frameworks in one place. The monitoring is incredible.',
      rating: 5
    },
    {
      name: 'Dr. Emily Watson',
      role: 'Research Lead at InnovateLabs',
      content: 'The speed and reliability have transformed how we approach AI research and deployment.',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
                <Zap className="w-4 h-4 mr-2" />
                Powered by Cerebras AI - Ultra-Fast Inference
              </div>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight">
              Build AI Agents at
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {' '}Lightning Speed
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto">
              The ultimate platform for AI developers. Create, deploy, and monitor intelligent agents 
              with multi-framework support and enterprise-grade infrastructure.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Link href="/auth">
                <Button size="lg" className="px-8 py-4 text-lg">
                  Start Building Free
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg">
                  View Pricing
                </Button>
              </Link>
            </div>

            <div className="flex items-center justify-center space-x-8 text-sm text-gray-500">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Free to start
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                No credit card required
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                Deploy in minutes
              </div>
            </div>
          </motion.div>
        </div>

        {/* Floating Dashboard Preview */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
          className="max-w-6xl mx-auto mt-16 px-4"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl blur-3xl opacity-20"></div>
            <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="bg-gray-900 rounded-xl p-6 text-white">
                  <h3 className="text-lg font-semibold mb-4">Live Execution</h3>
                  <div className="font-mono text-sm space-y-1">
                    <div className="text-green-400">[11:23:45] Agent initialized</div>
                    <div className="text-blue-400">[11:23:46] Processing at 450 t/s</div>
                    <div className="text-green-400">[11:23:48] ████████ 100%</div>
                  </div>
                </div>
                <div className="bg-blue-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4">Performance</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span>Speed</span>
                      <span className="font-semibold">450 tokens/sec</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cost</span>
                      <span className="font-semibold">$0.12/1K tokens</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Uptime</span>
                      <span className="font-semibold text-green-600">99.9%</span>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4">Frameworks</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-white rounded-lg p-2 text-center text-sm">LangChain</div>
                    <div className="bg-white rounded-lg p-2 text-center text-sm">AutoGen</div>
                    <div className="bg-white rounded-lg p-2 text-center text-sm">Semantic Kernel</div>
                    <div className="bg-white rounded-lg p-2 text-center text-sm">LlamaIndex</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Everything you need to build AI agents
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From development to deployment, we provide all the tools and infrastructure 
              to build production-ready AI applications.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow border-0 bg-gradient-to-br from-gray-50 to-gray-100">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mb-4">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted by AI developers worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about the platform
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-gray-600 mb-6 italic">
                      "{testimonial.content}"
                    </p>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-500">{testimonial.role}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to build the future?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of developers building amazing AI applications
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button size="lg" variant="secondary" className="px-8 py-4 text-lg">
                  Start Building Now
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button size="lg" variant="outline" className="px-8 py-4 text-lg border-white text-white hover:bg-white hover:text-blue-600">
                  View Pricing Plans
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

// ===================================

// src/app/auth/page.tsx
'use client';

import { useState } from 'react';
import { AuthForm } from '@/components/auth/auth-form';

export default function AuthPage() {
  const [mode, setMode] = useState<'signin' | 'signup'>('signin');

  return <AuthForm mode={mode} onModeChange={setMode} />;
}

// ===================================

// src/app/pricing/page.tsx
'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Zap, Crown, Building } from 'lucide-react';
import { SUBSCRIPTION_PLANS } from '@/lib/stripe';
import { formatCurrency } from '@/lib/utils';

export default function PricingPage() {
  const plans = [
    {
      ...SUBSCRIPTION_PLANS.starter,
      icon: Zap,
      description: 'Perfect for individual developers and small projects',
      buttonText: 'Start Free Trial',
      buttonVariant: 'outline' as const,
    },
    {
      ...SUBSCRIPTION_PLANS.pro,
      icon: Crown,
      description: 'Ideal for growing teams and production applications',
      buttonText: 'Start Pro Trial',
      buttonVariant: 'default' as const,
    },
    {
      ...SUBSCRIPTION_PLANS.enterprise,
      icon: Building,
      description: 'For large organizations with custom requirements',
      buttonText: 'Contact Sales',
      buttonVariant: 'outline' as const,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 px-4">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Simple, transparent pricing
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Choose the perfect plan for your AI development needs. All plans include our core features 
            with different usage limits and support levels.
          </p>
          <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            <Check className="w-4 h-4 mr-2" />
            7-day free trial on all plans
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative ${plan.popular ? 'md:-mt-4 md:mb-4' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <Card className={`h-full ${plan.popular ? 'ring-2 ring-blue-500 shadow-xl' : 'shadow-lg'} bg-white`}>
                <CardHeader className="text-center pb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center ${
                    plan.popular ? 'bg-gradient-to-r from-blue-600 to-purple-600' : 'bg-gray-100'
                  }`}>
                    <plan.icon className={`w-8 h-8 ${plan.popular ? 'text-white' : 'text-gray-600'}`} />
                  </div>
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <p className="text-gray-600 mt-2">{plan.description}</p>
                  
                  <div className="mt-6">
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold">{formatCurrency(plan.price)}</span>
                      <span className="text-gray-500 ml-2">/month</span>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <Check className="w-5 h-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <div className="pt-6 border-t">
                    <div className="space-y-2 text-sm text-gray-500">
                      <div className="flex justify-between">
                        <span>AI Agents</span>
                        <span className="font-medium">
                          {plan.limits.agents === -1 ? 'Unlimited' : plan.limits.agents}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Monthly Tokens</span>
                        <span className="font-medium">
                          {plan.limits.tokensPerMonth === -1 ? 'Unlimited' : plan.limits.tokensPerMonth.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Monthly Executions</span>
                        <span className="font-medium">
                          {plan.limits.executions === -1 ? 'Unlimited' : plan.limits.executions.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Link href="/auth" className="block w-full">
                    <Button 
                      variant={plan.buttonVariant} 
                      className="w-full mt-6"
                      size="lg"
                    >
                      {plan.buttonText}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mt-20 max-w-4xl mx-auto"
        >
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently asked questions
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h3>
              <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">What happens if I exceed my limits?</h3>
              <p className="text-gray-600">You'll be notified when approaching limits. Executions pause until next billing cycle or plan upgrade.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Do you offer refunds?</h3>
              <p className="text-gray-600">Yes, we offer a 30-day money-back guarantee for all annual subscriptions.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Is there enterprise support?</h3>
              <p className="text-gray-600">Enterprise customers get dedicated support, custom integrations, and SLA guarantees.</p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

// ===================================

// src/app/dashboard/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { motion } from 'framer-motion';
import { 
  Brain, 
  Plus, 
  Play, 
  Pause,
  BarChart3, 
  Settings,
  Activity,
  Clock,
  DollarSign,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Navbar } from '@/components/layout/navbar';
import { Loading } from '@/components/layout/loading';
import { formatCurrency, formatNumber, formatTokens, timeAgo } from '@/lib/utils';
import { User, AIAgent, AgentExecution } from '@/types';

export default function DashboardPage() {
  const [user, setUser] = useState<User | null>(null);
  const [agents, setAgents] = useState<AIAgent[]>([]);
  const [recentExecutions, setRecentExecutions] = useState<AgentExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalAgents: 0,
    totalExecutions: 0,
    totalTokens: 0,
    totalCost: 0,
  });

  const supabase = createClientComponentClient();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Load user profile
      const profileResponse = await fetch('/api/user/profile');
      const profileData = await profileResponse.json();
      if (profileData.profile) {
        setUser(profileData.profile);
      }

      // Load agents
      const agentsResponse = await fetch('/api/agents?limit=6');
      const agentsData = await agentsResponse.json();
      if (agentsData.agents) {
        setAgents(agentsData.agents);
      }

      // Load analytics
      const analyticsResponse = await fetch('/api/analytics?days=30');
      const analyticsData = await analyticsResponse.json();
      if (analyticsData.summary) {
        setStats({
          totalAgents: agentsData.agents?.length || 0,
          totalExecutions: analyticsData.summary.total_executions,
          totalTokens: analyticsData.summary.total_tokens,
          totalCost: analyticsData.summary.total_cost,
        });
      }
      if (analyticsData.recent_executions) {
        setRecentExecutions(analyticsData.recent_executions);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Loading />;
  }

  const usagePercentage = user ? (user.usage_tokens / user.usage_limit) * 100 : 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar user={user} />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back{user?.full_name ? `, ${user.full_name}` : ''}
          </h1>
          <p className="text-gray-600">
            Here's what's happening with your AI agents today.
          </p>
        </motion.div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Agents</p>
                    <p className="text-3xl font-bold">{formatNumber(stats.totalAgents)}</p>
                  </div>
                  <Brain className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Executions</p>
                    <p className="text-3xl font-bold">{formatNumber(stats.totalExecutions)}</p>
                  </div>
                  <Activity className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Tokens Used</p>
                    <p className="text-3xl font-bold">{formatTokens(stats.totalTokens)}</p>
                  </div>
                  <Zap className="w-8 h-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Cost</p>
                    <p className="text-3xl font-bold">{formatCurrency(stats.totalCost)}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Usage Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2" />
                  Usage Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Token Usage</span>
                      <span>{Math.round(usagePercentage)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          usagePercentage > 90 ? 'bg-red-500' : 
                          usagePercentage > 70 ? 'bg-yellow-500' : 
                          'bg-blue-500'
                        }`}
                        style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatTokens(user?.usage_tokens || 0)} / {formatTokens(user?.usage_limit || 0)} tokens
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="text-sm text-gray-600 mb-2">Current Plan</div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium capitalize">{user?.plan || 'Free'}</span>
                      <Button variant="outline" size="sm">
                        Upgrade
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                {recentExecutions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No recent activity</p>
                    <p className="text-sm">Create an agent to get started</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentExecutions.slice(0, 5).map((execution) => (
                      <div key={execution.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full ${
                            execution.status === 'success' ? 'bg-green-500' :
                            execution.status === 'error' ? 'bg-red-500' :
                            execution.status === 'running' ? 'bg-blue-500' :
                            'bg-gray-500'
                          }`} />
                          <div>
                            <p className="font-medium text-sm">
                              {(execution as any).ai_agents?.name || 'Unknown Agent'}
                            </p>
                            <p className="text-xs text-gray-500">
                              {timeAgo(execution.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right text-sm">
                          <div className="font-medium">
                            {formatTokens(execution.tokens_used)} tokens
                          </div>
                          <div className="text-gray-500">
                            {formatCurrency(execution.cost_usd)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Your Agents */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mt-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Your Agents</h2>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Agent
            </Button>
          </div>

          {agents.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No agents yet</h3>
                <p className="text-gray-600 mb-6">Create your first AI agent to get started with the platform</p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Agent
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {agents.map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                >
                  <Card className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-bold text-gray-900 truncate">{agent.name}</h3>
                        <div className={`w-3 h-3 rounded-full ${
                          agent.status === 'running' ? 'bg-green-500' :
                          agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                        }`} />
                      </div>
                      
                      {agent.description && (
                        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                          {agent.description}
                        </p>
                      )}
                      
                      <div className="space-y-2 text-sm text-gray-600 mb-4">
                        <div className="flex justify-between">
                          <span>Framework:</span>
                          <span className="font-medium capitalize">{agent.framework}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Model:</span>
                          <span className="font-medium">{agent.model}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Created:</span>
                          <span className="font-medium">{timeAgo(agent.created_at)}</span>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button size="sm" className="flex-1">
                          <Play className="w-4 h-4 mr-1" />
                          Run
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </main>
    </div>
  );
}