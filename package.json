{"name": "ai-orchestration-platform", "version": "1.0.0", "description": "Premium AI Agent Orchestration Platform with Cerebras Integration", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "format": "prettier --write .", "preview": "npm run build && npm run start"}, "dependencies": {"next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.10.0", "framer-motion": "^10.18.0", "zustand": "^4.4.7", "@tanstack/react-query": "^5.17.0", "lucide-react": "^0.263.1", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.0", "recharts": "^2.10.0", "@supabase/supabase-js": "^2.38.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "stripe": "^14.9.0", "@stripe/stripe-js": "^2.4.0", "date-fns": "^2.30.0", "uuid": "^9.0.1", "@types/uuid": "^9.0.7", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^0.1.0", "@radix-ui/react-card": "^0.1.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-input": "^0.1.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-textarea": "^0.1.0", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-next": "^14.1.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "keywords": ["ai", "agents", "orchestration", "cerebras", "react", "nextjs", "typescript", "saas"], "author": "AI Orchestration Platform", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-username/ai-orchestration-platform.git"}}