#!/bin/bash

# 🚀 AI Orchestration Platform - Complete Production Setup
# This script sets up everything you need for a production-ready SaaS

echo "🎯 Setting up AI Orchestration Platform..."

# 1. Initialize Next.js project with all required configurations
npx create-next-app@latest ai-orchestration-platform --typescript --tailwind --eslint --app --src-dir
cd ai-orchestration-platform

# 2. Install all production dependencies
echo "📦 Installing dependencies..."

# Core Next.js and React
npm install next@latest react@latest react-dom@latest

# TypeScript and types
npm install typescript @types/react @types/react-dom @types/node

# UI and Animation
npm install framer-motion lucide-react class-variance-authority clsx tailwind-merge
npm install @headlessui/react @heroicons/react
npm install react-syntax-highlighter @types/react-syntax-highlighter
npm install recharts

# State Management and Data Fetching
npm install zustand @tanstack/react-query

# Authentication and Database
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install @supabase/auth-ui-react @supabase/auth-ui-shared

# Payment Processing
npm install stripe @stripe/stripe-js

# Utilities
npm install date-fns uuid @types/uuid
npm install react-hot-toast
npm install react-hook-form @hookform/resolvers zod

# Development dependencies
npm install -D prettier prettier-plugin-tailwindcss
npm install -D @types/react-syntax-highlighter

# 3. Create environment variables file
echo "🔧 Setting up environment variables..."
cat > .env.local << 'EOF'
# Cerebras AI API Configuration
CEREBRAS_API_KEY=your_cerebras_api_key_here

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Optional: Analytics
NEXT_PUBLIC_ANALYTICS_ID=
EOF

# 4. Create project directory structure
echo "📁 Creating project structure..."
mkdir -p src/components/ui
mkdir -p src/components/dashboard
mkdir -p src/components/agent
mkdir -p src/components/auth
mkdir -p src/components/pricing
mkdir -p src/lib
mkdir -p src/hooks
mkdir -p src/types
mkdir -p src/store
mkdir -p src/app/api/auth
mkdir -p src/app/api/agents
mkdir -p src/app/api/stripe
mkdir -p src/app/api/executions
mkdir -p src/app/dashboard
mkdir -p src/app/auth
mkdir -p src/app/pricing
mkdir -p src/app/api/webhooks
mkdir -p public/images

# 5. Create Supabase schema file
echo "🗄️ Creating database schema..."
cat > supabase_schema.sql << 'EOF'
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'starter', 'pro', 'enterprise')),
  usage_tokens INTEGER DEFAULT 0,
  usage_limit INTEGER DEFAULT 1000,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- AI Agents table
CREATE TABLE ai_agents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  framework TEXT NOT NULL CHECK (framework IN ('langchain', 'autogen', 'semantic-kernel', 'llama-index')),
  model TEXT NOT NULL,
  configuration JSONB NOT NULL DEFAULT '{}',
  status TEXT DEFAULT 'idle' CHECK (status IN ('idle', 'running', 'error', 'paused')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Agent Executions table
CREATE TABLE agent_executions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  agent_id UUID REFERENCES ai_agents(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  input_text TEXT NOT NULL,
  output_text TEXT,
  tokens_used INTEGER DEFAULT 0,
  execution_time_ms INTEGER DEFAULT 0,
  cost_usd DECIMAL(10,6) DEFAULT 0,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'success', 'error')),
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Subscriptions table
CREATE TABLE subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  plan TEXT NOT NULL CHECK (plan IN ('starter', 'pro', 'enterprise')),
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);

-- Usage Analytics table
CREATE TABLE usage_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  agent_id UUID REFERENCES ai_agents(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  executions_count INTEGER DEFAULT 0,
  tokens_used INTEGER DEFAULT 0,
  cost_usd DECIMAL(10,6) DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
  UNIQUE(user_id, agent_id, date)
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- AI Agents policies
CREATE POLICY "Users can view own agents" ON ai_agents FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own agents" ON ai_agents FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own agents" ON ai_agents FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own agents" ON ai_agents FOR DELETE USING (auth.uid() = user_id);

-- Agent Executions policies
CREATE POLICY "Users can view own executions" ON agent_executions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own executions" ON agent_executions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Subscriptions policies
CREATE POLICY "Users can view own subscriptions" ON subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own subscriptions" ON subscriptions FOR UPDATE USING (auth.uid() = user_id);

-- Usage Analytics policies
CREATE POLICY "Users can view own analytics" ON usage_analytics FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own analytics" ON usage_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create functions and triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ai_agents_updated_at BEFORE UPDATE ON ai_agents
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX idx_ai_agents_user_id ON ai_agents(user_id);
CREATE INDEX idx_agent_executions_user_id ON agent_executions(user_id);
CREATE INDEX idx_agent_executions_agent_id ON agent_executions(agent_id);
CREATE INDEX idx_agent_executions_created_at ON agent_executions(created_at);
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX idx_usage_analytics_user_id_date ON usage_analytics(user_id, date);

-- Insert sample data for development (optional)
-- This will be populated when users sign up
EOF

echo "✅ Project setup complete!"
echo ""
echo "🔥 Next Steps:"
echo "1. Sign up for required services:"
echo "   • Cerebras AI: https://cloud.cerebras.ai/"
echo "   • Supabase: https://supabase.com/"
echo "   • Stripe: https://stripe.com/"
echo ""
echo "2. Update .env.local with your API keys"
echo "3. Run the SQL schema in your Supabase SQL editor"
echo "4. Start development: npm run dev"
echo ""
echo "🚀 Ready to build your SaaS empire!"