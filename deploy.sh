#!/bin/bash

# AI Orchestration Platform Deployment Script
# This script helps deploy the application to Vercel

echo "🚀 AI Orchestration Platform Deployment"
echo "========================================"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the project root."
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Run type checking
echo "🔍 Running type check..."
npm run type-check

if [ $? -ne 0 ]; then
    echo "❌ Type check failed. Please fix the errors before deploying."
    exit 1
fi

# Run linting
echo "🧹 Running linter..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  Linting issues found. Consider fixing them before deploying."
fi

# Build the project
echo "🏗️  Building project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors before deploying."
    exit 1
fi

# Deploy to Vercel
echo "🚀 Deploying to Vercel..."
vercel --prod

echo "✅ Deployment complete!"
echo ""
echo "📋 Post-deployment checklist:"
echo "1. Set up environment variables in Vercel dashboard"
echo "2. Configure Supabase database"
echo "3. Set up Stripe webhooks"
echo "4. Test the application"
echo ""
echo "🔗 Don't forget to:"
echo "- Add your domain to Supabase Auth settings"
echo "- Configure Stripe webhook endpoint"
echo "- Set up monitoring and alerts"
