'use client';

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Navbar } from '@/components/layout/navbar';
import { useAuth } from '@/hooks/use-auth';
import { 
  Brain, 
  Zap, 
  Shield, 
  BarChart3, 
  Rocket, 
  Users,
  CheckCircle,
  ArrowRight,
  Star,
  Globe,
  Clock,
  DollarSign
} from 'lucide-react';

export default function HomePage() {
  const { user } = useAuth();

  const features = [
    {
      icon: <Brain className="h-8 w-8 text-blue-600" />,
      title: 'Multi-Framework Support',
      description: 'LangChain, AutoGen, Semantic Kernel, and LlamaIndex - all in one platform.',
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      title: 'Ultra-Fast Inference',
      description: 'Cerebras AI integration delivers 450-1800 tokens/second for lightning-fast responses.',
    },
    {
      icon: <Shield className="h-8 w-8 text-green-600" />,
      title: 'Enterprise Security',
      description: 'SOC 2 compliant with end-to-end encryption and enterprise-grade security.',
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-purple-600" />,
      title: 'Real-Time Analytics',
      description: 'Comprehensive monitoring, performance tracking, and usage analytics.',
    },
    {
      icon: <Rocket className="h-8 w-8 text-red-600" />,
      title: 'Auto-Scaling',
      description: 'From prototypes to production workloads with automatic scaling.',
    },
    {
      icon: <Users className="h-8 w-8 text-indigo-600" />,
      title: 'Developer-First APIs',
      description: 'RESTful APIs, webhooks, and SDKs for seamless integration.',
    },
  ];

  const stats = [
    { label: 'Tokens Processed', value: '10M+', icon: <Zap className="h-5 w-5" /> },
    { label: 'Active Developers', value: '1,000+', icon: <Users className="h-5 w-5" /> },
    { label: 'Uptime SLA', value: '99.9%', icon: <Shield className="h-5 w-5" /> },
    { label: 'Response Time', value: '<100ms', icon: <Clock className="h-5 w-5" /> },
  ];

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'AI Engineer at TechCorp',
      content: 'This platform reduced our AI development time by 80%. The multi-framework support is game-changing.',
      rating: 5,
    },
    {
      name: 'Marcus Rodriguez',
      role: 'CTO at StartupXYZ',
      content: 'Incredible performance with Cerebras integration. Our agents respond in milliseconds.',
      rating: 5,
    },
    {
      name: 'Emily Watson',
      role: 'ML Lead at Enterprise Inc',
      content: 'Enterprise-grade security and monitoring gave us confidence to deploy at scale.',
      rating: 5,
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navbar user={user} />
      
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-100">
              🚀 Now with Cerebras AI Integration
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-6">
              Build AI Agents at
              <span className="gradient-text block">Lightning Speed</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              The only platform you need to create, deploy, and scale AI agents with 
              multi-framework support and enterprise-grade infrastructure.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href={user ? '/dashboard' : '/auth/signup'}>
                <Button size="lg" className="text-lg px-8 py-4">
                  {user ? 'Go to Dashboard' : 'Start Building Free'}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button variant="outline" size="lg" className="text-lg px-8 py-4">
                  View Pricing
                </Button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat) => (
                <div key={stat.label} className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    {stat.icon}
                    <span className="text-2xl font-bold text-gray-900 ml-2">{stat.value}</span>
                  </div>
                  <p className="text-sm text-gray-600">{stat.label}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything you need to build AI agents
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From rapid prototyping to enterprise deployment, our platform provides 
              all the tools and infrastructure you need.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mb-4">{feature.icon}</div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted by developers worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our community is saying about the platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <CardDescription className="text-base italic">
                    "{testimonial.content}"
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.name}</p>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Ready to revolutionize your AI development?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of developers building the future with intelligent agents.
            Start your free trial today.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={user ? '/dashboard' : '/auth/signup'}>
              <Button size="lg" variant="secondary" className="text-lg px-8 py-4">
                {user ? 'Go to Dashboard' : 'Start Free Trial'}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/pricing">
              <Button size="lg" variant="outline" className="text-lg px-8 py-4 text-white border-white hover:bg-white hover:text-blue-600">
                View Pricing Plans
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center mb-4">
                <Brain className="h-8 w-8 text-blue-400" />
                <span className="ml-2 text-xl font-bold">AI Orchestration</span>
              </div>
              <p className="text-gray-400">
                The premier platform for building and deploying AI agents at scale.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/features" className="hover:text-white">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-white">Pricing</Link></li>
                <li><Link href="/docs" className="hover:text-white">Documentation</Link></li>
                <li><Link href="/api" className="hover:text-white">API Reference</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/about" className="hover:text-white">About</Link></li>
                <li><Link href="/blog" className="hover:text-white">Blog</Link></li>
                <li><Link href="/careers" className="hover:text-white">Careers</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white">Help Center</Link></li>
                <li><Link href="/community" className="hover:text-white">Community</Link></li>
                <li><Link href="/status" className="hover:text-white">Status</Link></li>
                <li><Link href="/security" className="hover:text-white">Security</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 AI Orchestration Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
