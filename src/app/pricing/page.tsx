'use client';

import { Navbar } from '@/components/layout/navbar';
import { PricingPlans } from '@/components/billing/pricing-plans';
import { useAuth } from '@/hooks/use-auth';

export default function PricingPage() {
  const { user } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar user={user} />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <PricingPlans currentPlan={user?.plan} />
      </main>
    </div>
  );
}
