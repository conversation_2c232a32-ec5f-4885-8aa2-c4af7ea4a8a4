'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Navbar } from '@/components/layout/navbar';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/hooks/use-auth';
import { useAgents } from '@/hooks/use-agents';
import { useAnalytics } from '@/hooks/use-analytics';
import { Loading } from '@/components/layout/loading';
import { 
  Brain, 
  Plus, 
  Activity, 
  Zap, 
  DollarSign, 
  TrendingUp,
  Play,
  BarChart3
} from 'lucide-react';
import { formatCurrency, formatNumber, formatTokens, timeAgo } from '@/lib/utils';
import { PLAN_LIMITS } from '@/lib/constants';
import Link from 'next/link';

export default function DashboardPage() {
  const { user } = useAuth();
  const { data: agents, isLoading: agentsLoading } = useAgents();
  const { data: analytics, isLoading: analyticsLoading } = useAnalytics('7d');

  if (agentsLoading || analyticsLoading) {
    return <Loading />;
  }

  const userPlan = user?.plan || 'free';
  const limits = PLAN_LIMITS[userPlan as keyof typeof PLAN_LIMITS];
  const agentUsage = agents?.length || 0;
  const tokenUsage = user?.usage_tokens || 0;

  const agentUsagePercent = limits.agents === -1 ? 0 : (agentUsage / limits.agents) * 100;
  const tokenUsagePercent = (tokenUsage / limits.tokensPerMonth) * 100;

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back! Here's what's happening with your AI agents.</p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Link href="/agents/new">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="text-center">
                  <Plus className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                  <CardTitle>Create Agent</CardTitle>
                  <CardDescription>Build a new AI agent</CardDescription>
                </CardHeader>
              </Card>
            </Link>

            <Link href="/agents">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="text-center">
                  <Brain className="h-12 w-12 text-green-600 mx-auto mb-2" />
                  <CardTitle>Manage Agents</CardTitle>
                  <CardDescription>View and edit your agents</CardDescription>
                </CardHeader>
              </Card>
            </Link>

            <Link href="/analytics">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="text-center">
                  <BarChart3 className="h-12 w-12 text-purple-600 mx-auto mb-2" />
                  <CardTitle>View Analytics</CardTitle>
                  <CardDescription>Monitor performance</CardDescription>
                </CardHeader>
              </Card>
            </Link>
          </div>

          {/* Usage Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Plan Usage
                  <Badge variant={userPlan === 'free' ? 'secondary' : 'default'}>
                    {userPlan}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Agents</span>
                    <span>
                      {agentUsage} / {limits.agents === -1 ? '∞' : limits.agents}
                    </span>
                  </div>
                  <Progress value={agentUsagePercent} />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Tokens this month</span>
                    <span>
                      {formatTokens(tokenUsage)} / {formatTokens(limits.tokensPerMonth)}
                    </span>
                  </div>
                  <Progress value={tokenUsagePercent} />
                </div>

                {(agentUsagePercent > 80 || tokenUsagePercent > 80) && (
                  <div className="mt-4">
                    <Link href="/pricing">
                      <Button size="sm" className="w-full">
                        Upgrade Plan
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {analytics?.recentExecutions?.length > 0 ? (
                  <div className="space-y-3">
                    {analytics.recentExecutions.slice(0, 5).map((execution: any) => (
                      <div key={execution.id} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full ${
                            execution.status === 'success' ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          <div>
                            <p className="text-sm font-medium">{execution.ai_agents?.name}</p>
                            <p className="text-xs text-gray-500">{timeAgo(execution.created_at)}</p>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatTokens(execution.tokens_used)}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No recent activity</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Stats Cards */}
          {analytics?.summary && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Executions</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatNumber(analytics.summary.totalExecutions)}</div>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tokens Used</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatTokens(analytics.summary.totalTokens)}</div>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(analytics.summary.totalCost)}</div>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.summary.successRate.toFixed(1)}%</div>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Your Agents */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Your Agents</CardTitle>
                <Link href="/agents">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {agents && agents.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {agents.slice(0, 6).map((agent) => (
                    <div key={agent.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium truncate">{agent.name}</h3>
                        <div className={`w-2 h-2 rounded-full ${
                          agent.status === 'running' ? 'bg-green-500' :
                          agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                        }`} />
                      </div>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {agent.description || 'No description'}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {agent.framework}
                        </Badge>
                        <Link href={`/agents/${agent.id}`}>
                          <Button size="sm" variant="outline">
                            <Play className="h-3 w-3 mr-1" />
                            Run
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No agents yet</h3>
                  <p className="text-gray-600 mb-4">Create your first AI agent to get started</p>
                  <Link href="/agents/new">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Agent
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </main>
      </div>
    </ProtectedRoute>
  );
}
