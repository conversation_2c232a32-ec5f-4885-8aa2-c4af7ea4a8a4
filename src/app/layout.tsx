import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'AI Orchestration Platform',
  description: 'Build AI agents at lightning speed with multi-framework support and enterprise-grade infrastructure.',
  keywords: ['AI', 'agents', 'orchestration', 'cerebras', 'langchain', 'autogen'],
  authors: [{ name: 'AI Orchestration Platform' }],
  openGraph: {
    title: 'AI Orchestration Platform',
    description: 'Build AI agents at lightning speed',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
