import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '7d';
    
    const supabase = createSupabaseServerClient();

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '24h':
        startDate.setDate(endDate.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    // Get usage analytics
    const { data: analytics, error: analyticsError } = await supabase
      .from('usage_analytics')
      .select('*')
      .eq('user_id', user.id)
      .gte('date', startDateStr)
      .lte('date', endDateStr)
      .order('date', { ascending: true });

    if (analyticsError) {
      return NextResponse.json(
        { error: 'Failed to fetch analytics' },
        { status: 500 }
      );
    }

    // Get agent executions for detailed metrics
    const { data: executions, error: executionsError } = await supabase
      .from('agent_executions')
      .select(`
        *,
        ai_agents (
          name,
          framework,
          model
        )
      `)
      .eq('user_id', user.id)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });

    if (executionsError) {
      return NextResponse.json(
        { error: 'Failed to fetch executions' },
        { status: 500 }
      );
    }

    // Calculate summary metrics
    const totalExecutions = executions?.length || 0;
    const totalTokens = analytics?.reduce((sum, item) => sum + item.tokens_used, 0) || 0;
    const totalCost = analytics?.reduce((sum, item) => sum + item.cost_usd, 0) || 0;
    
    const successfulExecutions = executions?.filter(e => e.status === 'success').length || 0;
    const errorExecutions = executions?.filter(e => e.status === 'error').length || 0;
    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;

    const avgExecutionTime = executions?.length > 0 
      ? executions.reduce((sum, e) => sum + (e.execution_time_ms || 0), 0) / executions.length
      : 0;

    // Group analytics by date for charts
    const dailyAnalytics = analytics?.reduce((acc, item) => {
      const date = item.date;
      if (!acc[date]) {
        acc[date] = {
          date,
          executions: 0,
          tokens: 0,
          cost: 0,
        };
      }
      acc[date].executions += item.executions_count;
      acc[date].tokens += item.tokens_used;
      acc[date].cost += item.cost_usd;
      return acc;
    }, {} as Record<string, any>) || {};

    const chartData = Object.values(dailyAnalytics);

    // Group by agent for agent performance
    const agentPerformance = executions?.reduce((acc, execution) => {
      const agentName = execution.ai_agents?.name || 'Unknown';
      if (!acc[agentName]) {
        acc[agentName] = {
          name: agentName,
          executions: 0,
          tokens: 0,
          cost: 0,
          avgTime: 0,
          successRate: 0,
          framework: execution.ai_agents?.framework,
          model: execution.ai_agents?.model,
        };
      }
      
      acc[agentName].executions += 1;
      acc[agentName].tokens += execution.tokens_used || 0;
      acc[agentName].cost += execution.cost_usd || 0;
      acc[agentName].avgTime += execution.execution_time_ms || 0;
      
      return acc;
    }, {} as Record<string, any>) || {};

    // Calculate averages and success rates for agents
    Object.values(agentPerformance).forEach((agent: any) => {
      agent.avgTime = agent.avgTime / agent.executions;
      const agentExecutions = executions?.filter(e => e.ai_agents?.name === agent.name) || [];
      const successful = agentExecutions.filter(e => e.status === 'success').length;
      agent.successRate = agentExecutions.length > 0 ? (successful / agentExecutions.length) * 100 : 0;
    });

    // Group by framework
    const frameworkStats = executions?.reduce((acc, execution) => {
      const framework = execution.ai_agents?.framework || 'Unknown';
      if (!acc[framework]) {
        acc[framework] = {
          name: framework,
          executions: 0,
          tokens: 0,
          cost: 0,
        };
      }
      
      acc[framework].executions += 1;
      acc[framework].tokens += execution.tokens_used || 0;
      acc[framework].cost += execution.cost_usd || 0;
      
      return acc;
    }, {} as Record<string, any>) || {};

    return NextResponse.json({
      summary: {
        totalExecutions,
        totalTokens,
        totalCost,
        successRate,
        avgExecutionTime,
        successfulExecutions,
        errorExecutions,
      },
      chartData,
      agentPerformance: Object.values(agentPerformance),
      frameworkStats: Object.values(frameworkStats),
      recentExecutions: executions?.slice(0, 10) || [],
    });

  } catch (error: any) {
    console.error('Analytics error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
