import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe } from '@/lib/stripe';
import { createSupabaseServiceClient } from '@/lib/supabase';
import Stripe from 'stripe';

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = headers().get('stripe-signature')!;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err: any) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseServiceClient();

    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const userId = session.metadata?.userId;

        if (!userId) {
          console.error('No userId in session metadata');
          break;
        }

        // Get the subscription
        const subscription = await stripe.subscriptions.retrieve(
          session.subscription as string
        );

        // Update user's subscription in database
        const { error } = await supabase
          .from('subscriptions')
          .upsert({
            user_id: userId,
            stripe_subscription_id: subscription.id,
            stripe_customer_id: subscription.customer as string,
            plan: getPlanFromPriceId(subscription.items.data[0].price.id),
            status: subscription.status,
            current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
            cancel_at_period_end: subscription.cancel_at_period_end,
          });

        if (error) {
          console.error('Error updating subscription:', error);
        }

        // Update user's plan in profiles
        const plan = getPlanFromPriceId(subscription.items.data[0].price.id);
        const { error: profileError } = await supabase
          .from('profiles')
          .update({ plan })
          .eq('id', userId);

        if (profileError) {
          console.error('Error updating user plan:', profileError);
        }

        break;
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;

        // Find user by customer ID
        const { data: existingSubscription } = await supabase
          .from('subscriptions')
          .select('user_id')
          .eq('stripe_customer_id', subscription.customer as string)
          .single();

        if (existingSubscription) {
          // Update subscription
          const { error } = await supabase
            .from('subscriptions')
            .update({
              plan: getPlanFromPriceId(subscription.items.data[0].price.id),
              status: subscription.status,
              current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
              current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
              cancel_at_period_end: subscription.cancel_at_period_end,
            })
            .eq('stripe_subscription_id', subscription.id);

          if (error) {
            console.error('Error updating subscription:', error);
          }

          // Update user's plan
          const plan = getPlanFromPriceId(subscription.items.data[0].price.id);
          const { error: profileError } = await supabase
            .from('profiles')
            .update({ plan })
            .eq('id', existingSubscription.user_id);

          if (profileError) {
            console.error('Error updating user plan:', profileError);
          }
        }

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;

        // Find user by customer ID
        const { data: existingSubscription } = await supabase
          .from('subscriptions')
          .select('user_id')
          .eq('stripe_customer_id', subscription.customer as string)
          .single();

        if (existingSubscription) {
          // Update subscription status
          const { error } = await supabase
            .from('subscriptions')
            .update({
              status: 'canceled',
            })
            .eq('stripe_subscription_id', subscription.id);

          if (error) {
            console.error('Error updating subscription:', error);
          }

          // Downgrade user to free plan
          const { error: profileError } = await supabase
            .from('profiles')
            .update({ plan: 'free' })
            .eq('id', existingSubscription.user_id);

          if (profileError) {
            console.error('Error updating user plan:', profileError);
          }
        }

        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error: any) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getPlanFromPriceId(priceId: string): string {
  const priceIdMap: Record<string, string> = {
    [process.env.STRIPE_STARTER_PRICE_ID || 'price_starter']: 'starter',
    [process.env.STRIPE_PRO_PRICE_ID || 'price_pro']: 'pro',
    [process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise']: 'enterprise',
  };

  return priceIdMap[priceId] || 'free';
}
