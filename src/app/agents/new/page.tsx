'use client';

import { Navbar } from '@/components/layout/navbar';
import { AgentForm } from '@/components/agent/agent-form';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/hooks/use-auth';
import { useRouter } from 'next/navigation';

export default function NewAgentPage() {
  const { user } = useAuth();
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/agents');
  };

  const handleCancel = () => {
    router.push('/agents');
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <AgentForm 
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </main>
      </div>
    </ProtectedRoute>
  );
}
