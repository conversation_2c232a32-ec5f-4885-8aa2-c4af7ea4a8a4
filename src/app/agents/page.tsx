'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Navbar } from '@/components/layout/navbar';
import { Loading } from '@/components/layout/loading';
import { AgentForm } from '@/components/agent/agent-form';
import { AgentExecutor } from '@/components/agent/agent-executor';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAgents } from '@/hooks/use-agents';
import { useAuth } from '@/hooks/use-auth';
import { 
  Plus, 
  Settings, 
  Play, 
  MoreVertical, 
  Edit,
  Trash2,
  Brain
} from 'lucide-react';
import { timeAgo } from '@/lib/utils';

export default function AgentsPage() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const { user } = useAuth();
  const { data: agents, isLoading } = useAgents();

  if (isLoading) {
    return <Loading />;
  }

  const selectedAgentData = selectedAgentId 
    ? agents?.find(a => a.id === selectedAgentId) 
    : null;

  if (showCreateForm) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar user={user} />
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-6">
              <Button 
                variant="outline" 
                onClick={() => setShowCreateForm(false)}
              >
                ← Back to Agents
              </Button>
            </div>
            <AgentForm 
              onSuccess={() => setShowCreateForm(false)}
              onCancel={() => setShowCreateForm(false)}
            />
          </main>
        </div>
      </ProtectedRoute>
    );
  }

  if (selectedAgentData) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50">
          <Navbar user={user} />
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="mb-6">
              <Button 
                variant="outline" 
                onClick={() => setSelectedAgentId(null)}
              >
                ← Back to Agents
              </Button>
            </div>
            <AgentExecutor agent={selectedAgentData} />
          </main>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">AI Agents</h1>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Agent
            </Button>
          </div>

          {!agents || agents.length === 0 ? (
            <Card>
              <CardContent className="text-center py-16">
                <Brain className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No agents yet</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Create your first AI agent to start building intelligent applications with 
                  our multi-framework platform.
                </p>
                <Button onClick={() => setShowCreateForm(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Agent
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {agents.map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="truncate">{agent.name}</CardTitle>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${
                            agent.status === 'running' ? 'bg-green-500' :
                            agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                          }`} />
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      {agent.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {agent.description}
                        </p>
                      )}
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Framework:</span>
                          <span className="font-medium capitalize">{agent.framework}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Model:</span>
                          <span className="font-medium">{agent.model}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Created:</span>
                          <span className="font-medium">{timeAgo(agent.created_at)}</span>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 mt-6">
                        <Button 
                          size="sm" 
                          className="flex-1"
                          onClick={() => setSelectedAgentId(agent.id)}
                        >
                          <Play className="w-4 h-4 mr-1" />
                          Execute
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}
