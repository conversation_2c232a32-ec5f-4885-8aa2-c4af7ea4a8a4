'use client';

import { Navbar } from '@/components/layout/navbar';
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/hooks/use-auth';

export default function AnalyticsPage() {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <Navbar user={user} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <AnalyticsDashboard />
        </main>
      </div>
    </ProtectedRoute>
  );
}
