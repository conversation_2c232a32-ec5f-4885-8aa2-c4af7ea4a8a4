export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  plan: 'free' | 'starter' | 'pro' | 'enterprise';
  usage_tokens: number;
  usage_limit: number;
  created_at: string;
  updated_at: string;
}

export interface AIAgent {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  framework: 'langchain' | 'autogen' | 'semantic-kernel' | 'llama-index';
  model: string;
  configuration: AgentConfiguration;
  status: 'idle' | 'running' | 'error' | 'paused';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentConfiguration {
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  tools?: string[];
  memoryType?: 'conversation' | 'summary' | 'entity';
  maxMemoryTokens?: number;
  [key: string]: any;
}

export interface AgentExecution {
  id: string;
  agent_id: string;
  user_id: string;
  input_text: string;
  output_text?: string;
  tokens_used: number;
  execution_time_ms: number;
  cost_usd: number;
  status: 'pending' | 'running' | 'success' | 'error';
  error_message?: string;
  metadata: Record<string, any>;
  created_at: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  plan: 'starter' | 'pro' | 'enterprise';
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  current_period_start?: string;
  current_period_end?: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

export interface UsageAnalytics {
  id: string;
  user_id: string;
  agent_id?: string;
  date: string;
  executions_count: number;
  tokens_used: number;
  cost_usd: number;
  created_at: string;
}

export interface Framework {
  id: string;
  name: string;
  description: string;
  icon: string;
  features: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  popular: boolean;
}

export interface Model {
  id: string;
  name: string;
  provider: 'cerebras' | 'openai' | 'anthropic';
  speed: string;
  costPer1kTokens: number;
  maxTokens: number;
  recommended: boolean;
  capabilities: string[];
}

export interface PlanFeatures {
  name: string;
  price: number;
  priceId: string;
  features: string[];
  limits: {
    agents: number;
    executions: number;
    tokensPerMonth: number;
  };
  popular?: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CerebrasResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage: {
    total_tokens: number;
    prompt_tokens: number;
    completion_tokens: number;
  };
  model: string;
  created: number;
}
