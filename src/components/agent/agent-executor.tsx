'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  Square, 
  Copy, 
  Download, 
  Clock,
  Zap,
  DollarSign,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useExecuteAgent } from '@/hooks/use-agents';
import { AIAgent } from '@/types';
import { formatCurrency, formatDuration, formatTokens } from '@/lib/utils';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import toast from 'react-hot-toast';

interface AgentExecutorProps {
  agent: AIAgent;
}

interface ExecutionResult {
  output: string;
  tokens_used: number;
  execution_time_ms: number;
  cost_usd: number;
  execution_id: string;
}

export function AgentExecutor({ agent }: AgentExecutorProps) {
  const [input, setInput] = useState('');
  const [results, setResults] = useState<ExecutionResult[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  const executeAgent = useExecuteAgent();

  const handleExecute = async () => {
    if (!input.trim()) {
      toast.error('Please enter some input');
      return;
    }

    setIsExecuting(true);
    
    executeAgent.mutate(
      { agentId: agent.id, input: input.trim() },
      {
        onSuccess: (result) => {
          setResults(prev => [result, ...prev]);
          setInput('');
        },
        onError: (error) => {
          console.error('Execution error:', error);
        },
        onSettled: () => {
          setIsExecuting(false);
        },
      }
    );
  };

  const handleStop = () => {
    setIsExecuting(false);
    toast.success('Execution stopped');
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const downloadResult = (result: ExecutionResult, index: number) => {
    const blob = new Blob([result.output], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agent-result-${index + 1}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Agent Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <div className={`w-3 h-3 rounded-full mr-3 ${
              agent.status === 'running' ? 'bg-green-500' :
              agent.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
            }`} />
            {agent.name}
          </CardTitle>
          <div className="text-sm text-gray-600">
            Framework: {agent.framework} • Model: {agent.model}
          </div>
        </CardHeader>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle>Input</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your prompt or question here..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            rows={4}
            disabled={isExecuting}
          />
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {input.length} characters
            </div>
            
            <div className="flex space-x-2">
              {isExecuting ? (
                <Button 
                  variant="destructive" 
                  onClick={handleStop}
                  disabled={!isExecuting}
                >
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              ) : (
                <Button 
                  onClick={handleExecute}
                  disabled={!input.trim() || isExecuting}
                >
                  <Play className="w-4 h-4 mr-2" />
                  Execute
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Execution Status */}
      <AnimatePresence>
        {isExecuting && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600" />
                  <div>
                    <div className="font-medium text-blue-900">Executing...</div>
                    <div className="text-sm text-blue-700">
                      Running {agent.name} with {agent.model}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Results */}
      {results.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Results</h3>
          
          {results.map((result, index) => (
            <motion.div
              key={result.execution_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      Result #{results.length - index}
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(result.output)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadResult(result, index)}
                      >
                        <Download className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center text-gray-600">
                      <Clock className="w-4 h-4 mr-1" />
                      {formatDuration(result.execution_time_ms)}
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Zap className="w-4 h-4 mr-1" />
                      {formatTokens(result.tokens_used)} tokens
                    </div>
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {formatCurrency(result.cost_usd)}
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="prose max-w-none">
                    {result.output.includes('```') ? (
                      <SyntaxHighlighter
                        language="text"
                        style={oneDark}
                        className="rounded-lg"
                      >
                        {result.output}
                      </SyntaxHighlighter>
                    ) : (
                      <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg">
                        {result.output}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {results.length === 0 && !isExecuting && (
        <Card>
          <CardContent className="text-center py-12">
            <Play className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to execute</h3>
            <p className="text-gray-600">
              Enter your input above and click Execute to run the agent
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
