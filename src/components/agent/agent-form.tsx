'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCreateAgent } from '@/hooks/use-agents';
import { AI_FRAMEWORKS } from '@/lib/constants';
import { CEREBRAS_MODELS } from '@/lib/cerebras';
import { LoadingSpinner } from '@/components/layout/loading';
import { Brain, Settings, Zap } from 'lucide-react';

const agentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  framework: z.string().min(1, 'Framework is required'),
  model: z.string().min(1, 'Model is required'),
  systemPrompt: z.string().min(1, 'System prompt is required'),
  temperature: z.number().min(0).max(2),
  maxTokens: z.number().min(1).max(128000),
});

type AgentFormData = z.infer<typeof agentSchema>;

interface AgentFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function AgentForm({ onSuccess, onCancel }: AgentFormProps) {
  const [selectedFramework, setSelectedFramework] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  
  const createAgent = useCreateAgent();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<AgentFormData>({
    resolver: zodResolver(agentSchema),
    defaultValues: {
      temperature: 0.7,
      maxTokens: 2048,
    },
  });

  const temperature = watch('temperature');
  const maxTokens = watch('maxTokens');

  const onSubmit = async (data: AgentFormData) => {
    try {
      await createAgent.mutateAsync({
        name: data.name,
        description: data.description,
        framework: data.framework,
        model: data.model,
        configuration: {
          temperature: data.temperature,
          maxTokens: data.maxTokens,
          systemPrompt: data.systemPrompt,
        },
      });
      
      onSuccess?.();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const selectedFrameworkData = AI_FRAMEWORKS.find(f => f.id === selectedFramework);
  const selectedModelData = CEREBRAS_MODELS.find(m => m.id === selectedModel);

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <Brain className="mx-auto h-12 w-12 text-blue-600 mb-4" />
        <h1 className="text-3xl font-bold text-gray-900">Create AI Agent</h1>
        <p className="text-gray-600 mt-2">
          Configure your AI agent with the framework and model of your choice
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Set up the basic details for your AI agent
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Agent Name *</Label>
                <Input
                  id="name"
                  placeholder="My AI Assistant"
                  {...register('name')}
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="framework">Framework *</Label>
                <Select
                  value={selectedFramework}
                  onValueChange={(value) => {
                    setSelectedFramework(value);
                    setValue('framework', value);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a framework" />
                  </SelectTrigger>
                  <SelectContent>
                    {AI_FRAMEWORKS.map((framework) => (
                      <SelectItem key={framework.id} value={framework.id}>
                        <div className="flex items-center">
                          <span className="mr-2">{framework.icon}</span>
                          {framework.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.framework && (
                  <p className="text-sm text-red-600">{errors.framework.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe what your agent does..."
                rows={3}
                {...register('description')}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            {selectedFrameworkData && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">
                  {selectedFrameworkData.name}
                </h4>
                <p className="text-sm text-blue-700 mb-3">
                  {selectedFrameworkData.description}
                </p>
                <div className="flex flex-wrap gap-2">
                  {selectedFrameworkData.features.map((feature) => (
                    <span
                      key={feature}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Model Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Model Configuration
            </CardTitle>
            <CardDescription>
              Choose the AI model and configure its parameters
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="model">Model *</Label>
              <Select
                value={selectedModel}
                onValueChange={(value) => {
                  setSelectedModel(value);
                  setValue('model', value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  {CEREBRAS_MODELS.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <div className="font-medium">{model.name}</div>
                          <div className="text-sm text-gray-500">
                            {model.speed} • ${model.costPer1MTokens}/1M tokens
                          </div>
                        </div>
                        {model.recommended && (
                          <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                            Recommended
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.model && (
                <p className="text-sm text-red-600">{errors.model.message}</p>
              )}
            </div>

            {selectedModelData && (
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">
                  {selectedModelData.name}
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-green-700">Speed:</span>
                    <span className="ml-2 font-medium">{selectedModelData.speed}</span>
                  </div>
                  <div>
                    <span className="text-green-700">Cost:</span>
                    <span className="ml-2 font-medium">
                      ${selectedModelData.costPer1MTokens}/1M tokens
                    </span>
                  </div>
                </div>
                <div className="mt-2">
                  <span className="text-green-700">Capabilities:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedModelData.capabilities.map((capability) => (
                      <span
                        key={capability}
                        className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded"
                      >
                        {capability}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="temperature">
                  Temperature: {temperature}
                </Label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={temperature}
                  onChange={(e) => setValue('temperature', parseFloat(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Lower values make output more focused, higher values more creative
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="maxTokens">
                  Max Tokens: {maxTokens.toLocaleString()}
                </Label>
                <input
                  type="range"
                  min="100"
                  max="8192"
                  step="100"
                  value={maxTokens}
                  onChange={(e) => setValue('maxTokens', parseInt(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Maximum number of tokens in the response
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Prompt */}
        <Card>
          <CardHeader>
            <CardTitle>System Prompt</CardTitle>
            <CardDescription>
              Define the behavior and personality of your AI agent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="systemPrompt">System Prompt *</Label>
              <Textarea
                id="systemPrompt"
                placeholder="You are a helpful AI assistant that..."
                rows={6}
                {...register('systemPrompt')}
              />
              {errors.systemPrompt && (
                <p className="text-sm text-red-600">{errors.systemPrompt.message}</p>
              )}
              <p className="text-xs text-gray-500">
                This prompt defines how your agent will behave and respond to users
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={createAgent.isPending}>
            {createAgent.isPending ? (
              <>
                <LoadingSpinner size="sm" />
                <span className="ml-2">Creating Agent...</span>
              </>
            ) : (
              'Create Agent'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
