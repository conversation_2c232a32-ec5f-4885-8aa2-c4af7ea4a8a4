'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Zap, Crown, Building } from 'lucide-react';
import { SUBSCRIPTION_PLANS } from '@/lib/stripe';
import { useAuthStore } from '@/store/auth';
import { LoadingSpinner } from '@/components/layout/loading';
import { formatCurrency } from '@/lib/utils';
import toast from 'react-hot-toast';

interface PricingPlansProps {
  currentPlan?: string;
}

export function PricingPlans({ currentPlan = 'free' }: PricingPlansProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const { user } = useAuthStore();

  const handleUpgrade = async (priceId: string, planName: string) => {
    if (!user) {
      toast.error('Please sign in to upgrade');
      return;
    }

    setLoading(planName);

    try {
      const response = await fetch('/api/billing/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ priceId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      window.location.href = data.url;

    } catch (error: any) {
      toast.error(error.message || 'Failed to start checkout');
    } finally {
      setLoading(null);
    }
  };

  const plans = [
    {
      name: 'Free',
      price: 0,
      description: 'Perfect for getting started',
      icon: <Zap className="h-6 w-6" />,
      features: [
        '5 AI Agents',
        '1,000 tokens/month',
        'Basic monitoring',
        'Community support',
        'Standard frameworks',
      ],
      limits: {
        agents: 5,
        executions: 100,
        tokensPerMonth: 1_000,
      },
      priceId: null,
      current: currentPlan === 'free',
    },
    {
      ...SUBSCRIPTION_PLANS.starter,
      icon: <Zap className="h-6 w-6" />,
      description: 'Great for small teams and projects',
      current: currentPlan === 'starter',
    },
    {
      ...SUBSCRIPTION_PLANS.pro,
      icon: <Crown className="h-6 w-6" />,
      description: 'Perfect for growing businesses',
      current: currentPlan === 'pro',
    },
    {
      ...SUBSCRIPTION_PLANS.enterprise,
      icon: <Building className="h-6 w-6" />,
      description: 'For large organizations',
      current: currentPlan === 'enterprise',
    },
  ];

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900">Choose Your Plan</h2>
        <p className="text-gray-600 mt-2">
          Scale your AI operations with the right plan for your needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.map((plan) => (
          <Card 
            key={plan.name} 
            className={`relative ${plan.popular ? 'border-blue-500 shadow-lg' : ''} ${
              plan.current ? 'ring-2 ring-green-500' : ''
            }`}
          >
            {plan.popular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-500 text-white">Most Popular</Badge>
              </div>
            )}
            
            {plan.current && (
              <div className="absolute -top-3 right-4">
                <Badge className="bg-green-500 text-white">Current Plan</Badge>
              </div>
            )}

            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {plan.icon}
              </div>
              <CardTitle className="text-xl">{plan.name}</CardTitle>
              <CardDescription>{plan.description}</CardDescription>
              <div className="mt-4">
                <span className="text-3xl font-bold">
                  {formatCurrency(plan.price)}
                </span>
                {plan.price > 0 && (
                  <span className="text-gray-500">/month</span>
                )}
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center text-sm">
                    <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>

              <div className="pt-4 border-t">
                <div className="text-sm text-gray-600 space-y-1">
                  <div>
                    <strong>Agents:</strong> {plan.limits.agents === -1 ? 'Unlimited' : plan.limits.agents}
                  </div>
                  <div>
                    <strong>Executions:</strong> {plan.limits.executions === -1 ? 'Unlimited' : plan.limits.executions.toLocaleString()}/month
                  </div>
                  <div>
                    <strong>Tokens:</strong> {plan.limits.tokensPerMonth.toLocaleString()}/month
                  </div>
                </div>
              </div>

              <div className="pt-4">
                {plan.current ? (
                  <Button className="w-full" disabled>
                    Current Plan
                  </Button>
                ) : plan.priceId ? (
                  <Button
                    className="w-full"
                    onClick={() => handleUpgrade(plan.priceId!, plan.name)}
                    disabled={loading === plan.name}
                  >
                    {loading === plan.name ? (
                      <>
                        <LoadingSpinner size="sm" />
                        <span className="ml-2">Processing...</span>
                      </>
                    ) : (
                      `Upgrade to ${plan.name}`
                    )}
                  </Button>
                ) : (
                  <Button className="w-full" variant="outline" disabled>
                    Free Plan
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-center text-sm text-gray-500">
        <p>All plans include:</p>
        <div className="flex justify-center space-x-6 mt-2">
          <span>✓ 99.9% Uptime SLA</span>
          <span>✓ Secure API access</span>
          <span>✓ Real-time monitoring</span>
          <span>✓ Usage analytics</span>
        </div>
      </div>
    </div>
  );
}
