import { CerebrasClient } from './cerebras';
import { createSupabaseServiceClient } from './supabase';
import { AIAgent, AgentExecution } from '@/types';
import { v4 as uuidv4 } from 'uuid';

export class AgentExecutor {
  private cerebras: CerebrasClient;
  private supabase = createSupabaseServiceClient();

  constructor() {
    this.cerebras = new CerebrasClient();
  }

  async executeAgent(
    agent: AIAgent,
    input: string,
    userId: string
  ): Promise<AgentExecution> {
    const executionId = uuidv4();
    const startTime = Date.now();

    // Create execution record
    const execution: Partial<AgentExecution> = {
      id: executionId,
      agent_id: agent.id,
      user_id: userId,
      input_text: input,
      status: 'running',
      metadata: {
        framework: agent.framework,
        model: agent.model,
        configuration: agent.configuration,
      },
    };

    const { error: insertError } = await this.supabase
      .from('agent_executions')
      .insert(execution);

    if (insertError) {
      throw new Error(`Failed to create execution record: ${insertError.message}`);
    }

    try {
      // Execute based on framework
      const result = await this.executeByFramework(agent, input);
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      const cost = this.cerebras.calculateCost(result.usage.total_tokens, agent.model);

      // Update execution record with results
      const updatedExecution: Partial<AgentExecution> = {
        output_text: result.choices[0].message.content,
        tokens_used: result.usage.total_tokens,
        execution_time_ms: executionTime,
        cost_usd: cost,
        status: 'success',
        metadata: {
          ...execution.metadata,
          finish_reason: result.choices[0].finish_reason,
          prompt_tokens: result.usage.prompt_tokens,
          completion_tokens: result.usage.completion_tokens,
        },
      };

      const { data: finalExecution, error: updateError } = await this.supabase
        .from('agent_executions')
        .update(updatedExecution)
        .eq('id', executionId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Failed to update execution record: ${updateError.message}`);
      }

      // Update user usage
      await this.updateUserUsage(userId, result.usage.total_tokens, cost);

      // Update usage analytics
      await this.updateUsageAnalytics(userId, agent.id, result.usage.total_tokens, cost);

      return finalExecution as AgentExecution;
    } catch (error: any) {
      // Update execution record with error
      await this.supabase
        .from('agent_executions')
        .update({
          status: 'error',
          error_message: error.message,
          execution_time_ms: Date.now() - startTime,
        })
        .eq('id', executionId);

      throw error;
    }
  }

  private async executeByFramework(agent: AIAgent, input: string) {
    const { systemPrompt, temperature, maxTokens } = agent.configuration;

    const messages = [
      { role: 'system' as const, content: systemPrompt },
      { role: 'user' as const, content: input },
    ];

    switch (agent.framework) {
      case 'langchain':
        return this.executeLangChain(agent, messages);
      case 'autogen':
        return this.executeAutoGen(agent, messages);
      case 'semantic-kernel':
        return this.executeSemanticKernel(agent, messages);
      case 'llama-index':
        return this.executeLlamaIndex(agent, messages);
      default:
        throw new Error(`Unsupported framework: ${agent.framework}`);
    }
  }

  private async executeLangChain(agent: AIAgent, messages: any[]) {
    // For now, we'll use direct Cerebras API calls
    // In a real implementation, you would integrate with LangChain
    return this.cerebras.createCompletion(
      agent.model,
      messages,
      {
        temperature: agent.configuration.temperature,
        max_tokens: agent.configuration.maxTokens,
      }
    );
  }

  private async executeAutoGen(agent: AIAgent, messages: any[]) {
    // For now, we'll use direct Cerebras API calls
    // In a real implementation, you would integrate with AutoGen
    return this.cerebras.createCompletion(
      agent.model,
      messages,
      {
        temperature: agent.configuration.temperature,
        max_tokens: agent.configuration.maxTokens,
      }
    );
  }

  private async executeSemanticKernel(agent: AIAgent, messages: any[]) {
    // For now, we'll use direct Cerebras API calls
    // In a real implementation, you would integrate with Semantic Kernel
    return this.cerebras.createCompletion(
      agent.model,
      messages,
      {
        temperature: agent.configuration.temperature,
        max_tokens: agent.configuration.maxTokens,
      }
    );
  }

  private async executeLlamaIndex(agent: AIAgent, messages: any[]) {
    // For now, we'll use direct Cerebras API calls
    // In a real implementation, you would integrate with LlamaIndex
    return this.cerebras.createCompletion(
      agent.model,
      messages,
      {
        temperature: agent.configuration.temperature,
        max_tokens: agent.configuration.maxTokens,
      }
    );
  }

  private async updateUserUsage(userId: string, tokens: number, cost: number) {
    const { error } = await this.supabase.rpc('update_user_usage', {
      user_id: userId,
      tokens_used: tokens,
      cost_incurred: cost,
    });

    if (error) {
      console.error('Failed to update user usage:', error);
    }
  }

  private async updateUsageAnalytics(
    userId: string,
    agentId: string,
    tokens: number,
    cost: number
  ) {
    const today = new Date().toISOString().split('T')[0];

    const { error } = await this.supabase.rpc('upsert_usage_analytics', {
      user_id: userId,
      agent_id: agentId,
      date: today,
      executions_count: 1,
      tokens_used: tokens,
      cost_usd: cost,
    });

    if (error) {
      console.error('Failed to update usage analytics:', error);
    }
  }
}
