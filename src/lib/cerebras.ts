import { CerebrasResponse } from '@/types';

interface CerebrasMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface CerebrasCompletionRequest {
  model: string;
  messages: CerebrasMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export class CerebrasClient {
  private apiKey: string;
  private baseUrl = 'https://api.cerebras.ai/v1';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.CEREBRAS_API_KEY!;
    if (!this.apiKey) {
      throw new Error('Cerebras API key is required');
    }
  }

  async createCompletion(
    model: string,
    messages: CerebrasMessage[],
    options: Partial<CerebrasCompletionRequest> = {}
  ): Promise<CerebrasResponse> {
    const payload: CerebrasCompletionRequest = {
      model,
      messages,
      temperature: options.temperature ?? 0.7,
      max_tokens: options.max_tokens ?? 2048,
      top_p: options.top_p ?? 1,
      frequency_penalty: options.frequency_penalty ?? 0,
      presence_penalty: options.presence_penalty ?? 0,
      stream: false,
      ...options,
    };

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(
        `Cerebras API error (${response.status}): ${errorData.error || response.statusText}`
      );
    }

    return response.json();
  }

  async validateApiKey(): Promise<boolean> {
    try {
      await this.createCompletion('llama3.1-8b', [
        { role: 'user', content: 'Test connection' }
      ], { max_tokens: 10 });
      return true;
    } catch (error) {
      console.error('Cerebras API key validation failed:', error);
      return false;
    }
  }

  // Calculate cost based on token usage
  calculateCost(tokens: number, model: string): number {
    const rates: Record<string, number> = {
      'llama3.1-8b': 0.10,   // $0.10 per 1M tokens
      'llama3.1-70b': 0.60,  // $0.60 per 1M tokens
    };
    
    const ratePerMillion = rates[model] || 0.60;
    return (tokens / 1_000_000) * ratePerMillion;
  }
}

// Available Cerebras models
export const CEREBRAS_MODELS = [
  {
    id: 'llama3.1-8b',
    name: 'Llama 3.1 8B',
    speed: '1,800 tokens/sec',
    costPer1MTokens: 0.10,
    maxTokens: 128_000,
    recommended: true,
    capabilities: ['Fast inference', 'General tasks', 'Code generation'],
  },
  {
    id: 'llama3.1-70b',
    name: 'Llama 3.1 70B',
    speed: '450 tokens/sec',
    costPer1MTokens: 0.60,
    maxTokens: 128_000,
    recommended: false,
    capabilities: ['Complex reasoning', 'Advanced tasks', 'Detailed analysis'],
  },
] as const;
