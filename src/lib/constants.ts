import { Framework } from '@/types';

export const AI_FRAMEWORKS: Framework[] = [
  {
    id: 'langchain',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Build applications with LLMs through composability',
    icon: '🦜',
    features: ['Chain composition', 'Memory management', 'Tool integration', 'Vector stores'],
    complexity: 'intermediate',
    popular: true,
  },
  {
    id: 'autogen',
    name: 'AutoGen',
    description: 'Multi-agent conversation framework',
    icon: '🤖',
    features: ['Multi-agent chat', 'Code execution', 'Human-in-the-loop', 'Group chat'],
    complexity: 'advanced',
    popular: true,
  },
  {
    id: 'semantic-kernel',
    name: 'Semantic Kernel',
    description: 'Microsoft\'s SDK for AI orchestration',
    icon: '🧠',
    features: ['Skill composition', 'Planning', 'Memory', 'Connectors'],
    complexity: 'intermediate',
    popular: false,
  },
  {
    id: 'llama-index',
    name: 'LlamaIndex',
    description: 'Data framework for LLM applications',
    icon: '🦙',
    features: ['Data ingestion', 'Indexing', 'Query engines', 'RAG'],
    complexity: 'beginner',
    popular: true,
  },
];

export const PLAN_LIMITS = {
  free: {
    agents: 5,
    executions: 100,
    tokensPerMonth: 1_000,
  },
  starter: {
    agents: 25,
    executions: 1_000,
    tokensPerMonth: 10_000,
  },
  pro: {
    agents: 100,
    executions: 10_000,
    tokensPerMonth: 100_000,
  },
  enterprise: {
    agents: -1, // unlimited
    executions: -1, // unlimited
    tokensPerMonth: 1_000_000,
  },
};

export const EXECUTION_STATUSES = {
  PENDING: 'pending',
  RUNNING: 'running',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

export const AGENT_STATUSES = {
  IDLE: 'idle',
  RUNNING: 'running',
  ERROR: 'error',
  PAUSED: 'paused',
} as const;

export const SUBSCRIPTION_STATUSES = {
  ACTIVE: 'active',
  CANCELED: 'canceled',
  INCOMPLETE: 'incomplete',
  INCOMPLETE_EXPIRED: 'incomplete_expired',
  PAST_DUE: 'past_due',
  TRIALING: 'trialing',
  UNPAID: 'unpaid',
} as const;
