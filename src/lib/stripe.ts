import Stripe from 'stripe';
import { PlanFeatures } from '@/types';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing STRIPE_SECRET_KEY environment variable');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

export const SUBSCRIPTION_PLANS: Record<string, PlanFeatures> = {
  starter: {
    name: 'Starter',
    price: 29,
    priceId: process.env.STRIPE_STARTER_PRICE_ID || 'price_starter',
    features: [
      '25 AI Agents',
      '10,000 tokens/month',
      'Basic monitoring',
      'Email support',
      'Standard frameworks',
    ],
    limits: {
      agents: 25,
      executions: 1_000,
      tokensPerMonth: 10_000,
    },
  },
  pro: {
    name: 'Pro',
    price: 99,
    priceId: process.env.STRIPE_PRO_PRICE_ID || 'price_pro',
    features: [
      '100 AI Agents',
      '100,000 tokens/month',
      'Advanced analytics',
      'Priority support',
      'All frameworks',
      'Custom models',
    ],
    limits: {
      agents: 100,
      executions: 10_000,
      tokensPerMonth: 100_000,
    },
    popular: true,
  },
  enterprise: {
    name: 'Enterprise',
    price: 499,
    priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise',
    features: [
      'Unlimited agents',
      '1M tokens/month',
      'White-label solution',
      '24/7 dedicated support',
      'Custom integrations',
      'On-premise deployment',
    ],
    limits: {
      agents: -1,
      executions: -1,
      tokensPerMonth: 1_000_000,
    },
  },
};

export async function createCheckoutSession(
  customerId: string,
  priceId: string,
  userId: string
): Promise<Stripe.Checkout.Session> {
  return await stripe.checkout.sessions.create({
    customer: customerId,
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?success=true`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?canceled=true`,
    metadata: {
      userId,
    },
  });
}

export async function createOrRetrieveCustomer(email: string, userId: string) {
  const existingCustomers = await stripe.customers.list({
    email,
    limit: 1,
  });

  if (existingCustomers.data.length > 0) {
    return existingCustomers.data[0];
  }

  return await stripe.customers.create({
    email,
    metadata: {
      userId,
    },
  });
}
