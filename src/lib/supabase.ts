import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Client component client (for use in client components)
export const createSupabaseClient = () => createClientComponentClient();

// Server component client (for use in server components)
export const createSupabaseServerClient = () => createServerComponentClient({ cookies });

// Service role client (for server-side operations)
export const createSupabaseServiceClient = () => {
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!serviceKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY');
  }
  return createClient(supabaseUrl, serviceKey);
};

// Database types
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          plan: string;
          usage_tokens: number;
          usage_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          plan?: string;
          usage_tokens?: number;
          usage_limit?: number;
        };
        Update: {
          full_name?: string | null;
          avatar_url?: string | null;
          plan?: string;
          usage_tokens?: number;
          usage_limit?: number;
        };
      };
      ai_agents: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          framework: string;
          model: string;
          configuration: any;
          status: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          name: string;
          description?: string | null;
          framework: string;
          model: string;
          configuration: any;
          status?: string;
          is_active?: boolean;
        };
        Update: {
          name?: string;
          description?: string | null;
          framework?: string;
          model?: string;
          configuration?: any;
          status?: string;
          is_active?: boolean;
        };
      };
      agent_executions: {
        Row: {
          id: string;
          agent_id: string;
          user_id: string;
          input_text: string;
          output_text: string | null;
          tokens_used: number;
          execution_time_ms: number;
          cost_usd: number;
          status: string;
          error_message: string | null;
          metadata: any;
          created_at: string;
        };
        Insert: {
          agent_id: string;
          user_id: string;
          input_text: string;
          output_text?: string | null;
          tokens_used?: number;
          execution_time_ms?: number;
          cost_usd?: number;
          status?: string;
          error_message?: string | null;
          metadata?: any;
        };
        Update: {
          output_text?: string | null;
          tokens_used?: number;
          execution_time_ms?: number;
          cost_usd?: number;
          status?: string;
          error_message?: string | null;
          metadata?: any;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          user_id: string;
          stripe_subscription_id: string | null;
          stripe_customer_id: string | null;
          plan: string;
          status: string;
          current_period_start: string | null;
          current_period_end: string | null;
          trial_end: string | null;
          cancel_at_period_end: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          plan: string;
          status: string;
          current_period_start?: string | null;
          current_period_end?: string | null;
          trial_end?: string | null;
          cancel_at_period_end?: boolean;
        };
        Update: {
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          plan?: string;
          status?: string;
          current_period_start?: string | null;
          current_period_end?: string | null;
          trial_end?: string | null;
          cancel_at_period_end?: boolean;
        };
      };
      usage_analytics: {
        Row: {
          id: string;
          user_id: string;
          agent_id: string | null;
          date: string;
          executions_count: number;
          tokens_used: number;
          cost_usd: number;
          created_at: string;
        };
        Insert: {
          user_id: string;
          agent_id?: string | null;
          date: string;
          executions_count?: number;
          tokens_used?: number;
          cost_usd?: number;
        };
        Update: {
          executions_count?: number;
          tokens_used?: number;
          cost_usd?: number;
        };
      };
    };
  };
};
