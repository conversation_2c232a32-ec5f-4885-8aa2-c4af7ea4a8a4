'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createSupabaseClient } from '@/lib/supabase';
import { useAuthStore } from '@/store/auth';
import { AIAgent, AgentConfiguration } from '@/types';
import toast from 'react-hot-toast';

export function useAgents() {
  const { user } = useAuthStore();
  const supabase = createSupabaseClient();

  return useQuery({
    queryKey: ['agents', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('ai_agents')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as AIAgent[];
    },
    enabled: !!user,
  });
}

export function useCreateAgent() {
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  const supabase = createSupabaseClient();

  return useMutation({
    mutationFn: async (agentData: {
      name: string;
      description?: string;
      framework: string;
      model: string;
      configuration: AgentConfiguration;
    }) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('ai_agents')
        .insert({
          user_id: user.id,
          ...agentData,
          status: 'idle',
          is_active: true,
        })
        .select()
        .single();

      if (error) throw error;
      return data as AIAgent;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent created successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create agent');
    },
  });
}

export function useUpdateAgent() {
  const queryClient = useQueryClient();
  const supabase = createSupabaseClient();

  return useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<AIAgent> }) => {
      const { data, error } = await supabase
        .from('ai_agents')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data as AIAgent;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent updated successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update agent');
    },
  });
}

export function useDeleteAgent() {
  const queryClient = useQueryClient();
  const supabase = createSupabaseClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('ai_agents')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['agents'] });
      toast.success('Agent deleted successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete agent');
    },
  });
}

export function useExecuteAgent() {
  const { user } = useAuthStore();
  const supabase = createSupabaseClient();

  return useMutation({
    mutationFn: async ({ agentId, input }: { agentId: string; input: string }) => {
      if (!user) throw new Error('User not authenticated');

      // Call the execution API
      const response = await fetch('/api/agents/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentId,
          input,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Execution failed');
      }

      return response.json();
    },
    onError: (error: any) => {
      toast.error(error.message || 'Execution failed');
    },
  });
}
