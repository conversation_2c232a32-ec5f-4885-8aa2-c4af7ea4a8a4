'use client';

import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store/auth';

export function useAnalytics(period: string = '7d') {
  const { user } = useAuthStore();

  return useQuery({
    queryKey: ['analytics', user?.id, period],
    queryFn: async () => {
      const response = await fetch(`/api/analytics?period=${period}`);
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      return response.json();
    },
    enabled: !!user,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
}
