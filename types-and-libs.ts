// src/types/index.ts
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  plan: 'free' | 'starter' | 'pro' | 'enterprise';
  usage_tokens: number;
  usage_limit: number;
  created_at: string;
  updated_at: string;
}

export interface AIAgent {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  framework: 'langchain' | 'autogen' | 'semantic-kernel' | 'llama-index';
  model: string;
  configuration: AgentConfiguration;
  status: 'idle' | 'running' | 'error' | 'paused';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface AgentConfiguration {
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  tools?: string[];
  memoryType?: 'conversation' | 'summary' | 'entity';
  maxMemoryTokens?: number;
  [key: string]: any;
}

export interface AgentExecution {
  id: string;
  agent_id: string;
  user_id: string;
  input_text: string;
  output_text?: string;
  tokens_used: number;
  execution_time_ms: number;
  cost_usd: number;
  status: 'pending' | 'running' | 'success' | 'error';
  error_message?: string;
  metadata: Record<string, any>;
  created_at: string;
}

export interface Subscription {
  id: string;
  user_id: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  plan: 'starter' | 'pro' | 'enterprise';
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  current_period_start?: string;
  current_period_end?: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

export interface UsageAnalytics {
  id: string;
  user_id: string;
  agent_id?: string;
  date: string;
  executions_count: number;
  tokens_used: number;
  cost_usd: number;
  created_at: string;
}

export interface Framework {
  id: string;
  name: string;
  description: string;
  icon: string;
  features: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  popular: boolean;
}

export interface Model {
  id: string;
  name: string;
  provider: 'cerebras' | 'openai' | 'anthropic';
  speed: string;
  costPer1kTokens: number;
  maxTokens: number;
  recommended: boolean;
  capabilities: string[];
}

export interface PlanFeatures {
  name: string;
  price: number;
  priceId: string;
  features: string[];
  limits: {
    agents: number;
    executions: number;
    tokensPerMonth: number;
  };
  popular?: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CerebrasResponse {
  choices: Array<{
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }>;
  usage: {
    total_tokens: number;
    prompt_tokens: number;
    completion_tokens: number;
  };
  model: string;
  created: number;
}

// ===================================

// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient, createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Client component client (for use in client components)
export const createSupabaseClient = () => createClientComponentClient();

// Server component client (for use in server components)
export const createSupabaseServerClient = () => createServerComponentClient({ cookies });

// Service role client (for server-side operations)
export const createSupabaseServiceClient = () => {
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  if (!serviceKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY');
  }
  return createClient(supabaseUrl, serviceKey);
};

// Database types
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          plan: string;
          usage_tokens: number;
          usage_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          plan?: string;
          usage_tokens?: number;
          usage_limit?: number;
        };
        Update: {
          full_name?: string | null;
          avatar_url?: string | null;
          plan?: string;
          usage_tokens?: number;
          usage_limit?: number;
        };
      };
      ai_agents: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          framework: string;
          model: string;
          configuration: any;
          status: string;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          name: string;
          description?: string | null;
          framework: string;
          model: string;
          configuration: any;
          status?: string;
          is_active?: boolean;
        };
        Update: {
          name?: string;
          description?: string | null;
          framework?: string;
          model?: string;
          configuration?: any;
          status?: string;
          is_active?: boolean;
        };
      };
      agent_executions: {
        Row: {
          id: string;
          agent_id: string;
          user_id: string;
          input_text: string;
          output_text: string | null;
          tokens_used: number;
          execution_time_ms: number;
          cost_usd: number;
          status: string;
          error_message: string | null;
          metadata: any;
          created_at: string;
        };
        Insert: {
          agent_id: string;
          user_id: string;
          input_text: string;
          output_text?: string | null;
          tokens_used?: number;
          execution_time_ms?: number;
          cost_usd?: number;
          status?: string;
          error_message?: string | null;
          metadata?: any;
        };
        Update: {
          output_text?: string | null;
          tokens_used?: number;
          execution_time_ms?: number;
          cost_usd?: number;
          status?: string;
          error_message?: string | null;
          metadata?: any;
        };
      };
    };
  };
};

// ===================================

// src/lib/cerebras.ts
import { CerebrasResponse } from '@/types';

interface CerebrasMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface CerebrasCompletionRequest {
  model: string;
  messages: CerebrasMessage[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export class CerebrasClient {
  private apiKey: string;
  private baseUrl = 'https://api.cerebras.ai/v1';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.CEREBRAS_API_KEY!;
    if (!this.apiKey) {
      throw new Error('Cerebras API key is required');
    }
  }

  async createCompletion(
    model: string,
    messages: CerebrasMessage[],
    options: Partial<CerebrasCompletionRequest> = {}
  ): Promise<CerebrasResponse> {
    const payload: CerebrasCompletionRequest = {
      model,
      messages,
      temperature: options.temperature ?? 0.7,
      max_tokens: options.max_tokens ?? 2048,
      top_p: options.top_p ?? 1,
      frequency_penalty: options.frequency_penalty ?? 0,
      presence_penalty: options.presence_penalty ?? 0,
      stream: false,
      ...options,
    };

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(
        `Cerebras API error (${response.status}): ${errorData.error || response.statusText}`
      );
    }

    return response.json();
  }

  async validateApiKey(): Promise<boolean> {
    try {
      await this.createCompletion('llama3.1-8b', [
        { role: 'user', content: 'Test connection' }
      ], { max_tokens: 10 });
      return true;
    } catch (error) {
      console.error('Cerebras API key validation failed:', error);
      return false;
    }
  }

  // Calculate cost based on token usage
  calculateCost(tokens: number, model: string): number {
    const rates: Record<string, number> = {
      'llama3.1-8b': 0.10,   // $0.10 per 1M tokens
      'llama3.1-70b': 0.60,  // $0.60 per 1M tokens
    };
    
    const ratePerMillion = rates[model] || 0.60;
    return (tokens / 1_000_000) * ratePerMillion;
  }
}

// Available Cerebras models
export const CEREBRAS_MODELS = [
  {
    id: 'llama3.1-8b',
    name: 'Llama 3.1 8B',
    speed: '1,800 tokens/sec',
    costPer1MTokens: 0.10,
    maxTokens: 128_000,
    recommended: true,
    capabilities: ['Fast inference', 'General tasks', 'Code generation'],
  },
  {
    id: 'llama3.1-70b',
    name: 'Llama 3.1 70B',
    speed: '450 tokens/sec',
    costPer1MTokens: 0.60,
    maxTokens: 128_000,
    recommended: false,
    capabilities: ['Complex reasoning', 'Advanced tasks', 'Detailed analysis'],
  },
] as const;

// ===================================

// src/lib/stripe.ts
import Stripe from 'stripe';
import { PlanFeatures } from '@/types';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('Missing STRIPE_SECRET_KEY environment variable');
}

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

export const SUBSCRIPTION_PLANS: Record<string, PlanFeatures> = {
  starter: {
    name: 'Starter',
    price: 29,
    priceId: process.env.STRIPE_STARTER_PRICE_ID || 'price_starter',
    features: [
      '5 AI Agents',
      '10,000 tokens/month',
      'Basic monitoring',
      'Email support',
      'Standard frameworks',
    ],
    limits: {
      agents: 5,
      executions: 1_000,
      tokensPerMonth: 10_000,
    },
  },
  pro: {
    name: 'Pro',
    price: 99,
    priceId: process.env.STRIPE_PRO_PRICE_ID || 'price_pro',
    features: [
      '25 AI Agents',
      '100,000 tokens/month',
      'Advanced analytics',
      'Priority support',
      'All frameworks',
      'Custom models',
    ],
    limits: {
      agents: 25,
      executions: 10_000,
      tokensPerMonth: 100_000,
    },
    popular: true,
  },
  enterprise: {
    name: 'Enterprise',
    price: 499,
    priceId: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise',
    features: [
      'Unlimited agents',
      '1M tokens/month',
      'White-label solution',
      '24/7 dedicated support',
      'Custom integrations',
      'On-premise deployment',
    ],
    limits: {
      agents: -1,
      executions: -1,
      tokensPerMonth: 1_000_000,
    },
  },
};

export async function createCheckoutSession(
  customerId: string,
  priceId: string,
  userId: string
): Promise<Stripe.Checkout.Session> {
  return await stripe.checkout.sessions.create({
    customer: customerId,
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?success=true`,
    cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/pricing?canceled=true`,
    metadata: {
      userId,
    },
  });
}

export async function createOrRetrieveCustomer(email: string, userId: string) {
  const existingCustomers = await stripe.customers.list({
    email,
    limit: 1,
  });

  if (existingCustomers.data.length > 0) {
    return existingCustomers.data[0];
  }

  return await stripe.customers.create({
    email,
    metadata: {
      userId,
    },
  });
}

// ===================================

// src/lib/utils.ts
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
}

export function formatNumber(num: number): string {
  return new Intl.NumberFormat('en-US').format(num);
}

export function formatTokens(tokens: number): string {
  if (tokens < 1000) return tokens.toString();
  if (tokens < 1_000_000) return `${(tokens / 1000).toFixed(1)}K`;
  return `${(tokens / 1_000_000).toFixed(1)}M`;
}

export function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
}

export function calculateTokenCost(tokens: number, model: string): number {
  const rates: Record<string, number> = {
    'llama3.1-8b': 0.10,
    'llama3.1-70b': 0.60,
  };
  
  const ratePerMillion = rates[model] || 0.60;
  return (tokens / 1_000_000) * ratePerMillion;
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - 3) + '...';
}

export function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

// Date utilities
export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(new Date(date));
}

export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
}

export function timeAgo(date: string | Date): string {
  const now = new Date();
  const past = new Date(date);
  const diff = now.getTime() - past.getTime();

  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) return 'just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return formatDate(date);
}