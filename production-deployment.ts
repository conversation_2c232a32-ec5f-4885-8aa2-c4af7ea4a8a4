// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "regions": ["iad1", "sfo1"],
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/dashboard",
      "has": [
        {
          "type": "cookie",
          "key": "sb-access-token",
          "value": "(?<token>.*)"
        }
      ],
      "destination": "/dashboard",
      "permanent": false
    }
  ]
}

// ===================================

// Dockerfile (for containerized deployment)
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Next.js collects completely anonymous telemetry data about general usage.
ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]

// ===================================

// docker-compose.yml (for local development)
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - CEREBRAS_API_KEY=${CEREBRAS_API_KEY}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
    volumes:
      - .env.local:/app/.env.local:ro
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:

// ===================================

// .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run type check
        run: npm run type-check
      
      - name: Run linter
        run: npm run lint
      
      - name: Run tests
        run: npm test
        env:
          CI: true

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

// ===================================

// src/components/error-boundary.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // In production, you might want to log this to an error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600">
                We encountered an unexpected error. Our team has been notified.
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Error Details
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {this.state.error.stack}
                  </pre>
                </details>
              )}
              
              <div className="flex space-x-2">
                <Button
                  onClick={() => window.location.reload()}
                  className="flex-1"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reload Page
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/'}
                  className="flex-1"
                >
                  Go Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// ===================================

// src/lib/monitoring.ts
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  userId?: string;
  metadata?: Record<string, any>;
}

class MonitoringService {
  private metrics: PerformanceMetric[] = [];
  private isProduction = process.env.NODE_ENV === 'production';

  track(name: string, value: number, metadata?: Record<string, any>) {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      metadata,
    };

    this.metrics.push(metric);

    if (this.isProduction) {
      this.sendToAnalytics(metric);
    } else {
      console.log('📊 Metric:', metric);
    }
  }

  trackError(error: Error, context?: Record<string, any>) {
    const errorMetric: PerformanceMetric = {
      name: 'error',
      value: 1,
      timestamp: Date.now(),
      metadata: {
        message: error.message,
        stack: error.stack,
        ...context,
      },
    };

    this.metrics.push(errorMetric);

    if (this.isProduction) {
      this.sendToAnalytics(errorMetric);
    } else {
      console.error('🚨 Error tracked:', errorMetric);
    }
  }

  trackPageView(path: string, userId?: string) {
    this.track('page_view', 1, {
      path,
      userId,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });
  }

  trackAgentExecution(agentId: string, duration: number, tokensUsed: number) {
    this.track('agent_execution', 1, {
      agentId,
      duration,
      tokensUsed,
      timestamp: new Date().toISOString(),
    });
  }

  private async sendToAnalytics(metric: PerformanceMetric) {
    try {
      // In production, send to your analytics service
      // Example: PostHog, Mixpanel, Google Analytics, etc.
      
      if (process.env.NEXT_PUBLIC_ANALYTICS_ID) {
        await fetch('/api/analytics/track', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(metric),
        });
      }
    } catch (error) {
      console.error('Failed to send analytics:', error);
    }
  }

  getMetrics() {
    return this.metrics;
  }

  clearMetrics() {
    this.metrics = [];
  }
}

export const monitoring = new MonitoringService();

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const trackMetric = (name: string, value: number, metadata?: Record<string, any>) => {
    monitoring.track(name, value, metadata);
  };

  const trackError = (error: Error, context?: Record<string, any>) => {
    monitoring.trackError(error, context);
  };

  const trackPageView = (path: string, userId?: string) => {
    monitoring.trackPageView(path, userId);
  };

  return {
    trackMetric,
    trackError,
    trackPageView,
  };
}

// ===================================

// src/lib/cache.ts
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    };
    
    this.cache.set(key, entry);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  size(): number {
    return this.cache.size;
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

export const cache = new CacheService();

// Auto cleanup every 10 minutes
setInterval(() => {
  cache.cleanup();
}, 10 * 60 * 1000);

// ===================================

// src/app/api/health/route.ts
import { NextResponse } from 'next/server';
import { createSupabaseServiceClient } from '@/lib/supabase';
import { CerebrasClient } from '@/lib/cerebras';

export async function GET() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: 'unknown',
      cerebras: 'unknown',
      redis: 'unknown',
    },
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
  };

  try {
    // Check database connection
    const supabase = createSupabaseServiceClient();
    const { error: dbError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    health.services.database = dbError ? 'unhealthy' : 'healthy';
  } catch (error) {
    health.services.database = 'unhealthy';
  }

  try {
    // Check Cerebras API
    if (process.env.CEREBRAS_API_KEY) {
      const cerebras = new CerebrasClient();
      const isValid = await cerebras.validateApiKey();
      health.services.cerebras = isValid ? 'healthy' : 'unhealthy';
    }
  } catch (error) {
    health.services.cerebras = 'unhealthy';
  }

  // Determine overall health
  const unhealthyServices = Object.values(health.services).filter(
    status => status === 'unhealthy'
  );
  
  if (unhealthyServices.length > 0) {
    health.status = 'degraded';
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  
  return NextResponse.json(health, { status: statusCode });
}

// ===================================

// src/app/api/analytics/track/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const metric = await request.json();

    // In production, you might want to:
    // 1. Validate the metric data
    // 2. Send to external analytics service
    // 3. Store aggregated data in your database
    
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to PostHog, Mixpanel, etc.
      // await sendToAnalyticsService(metric);
    }

    // Store critical metrics in database for internal analytics
    if (['error', 'agent_execution', 'subscription_created'].includes(metric.name)) {
      await supabase
        .from('analytics_events')
        .insert({
          event_name: metric.name,
          event_data: metric.metadata,
          created_at: new Date(metric.timestamp).toISOString(),
        });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Analytics tracking error:', error);
    return NextResponse.json(
      { error: 'Failed to track metric' },
      { status: 500 }
    );
  }
}

// ===================================

// src/components/seo/meta-tags.tsx
import Head from 'next/head';

interface MetaTagsProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
}

export function MetaTags({
  title = 'AI Orchestration Platform - Build AI Agents at Lightning Speed',
  description = 'Enterprise-ready platform for building, deploying, and monitoring AI agents with multi-framework support and ultra-fast inference.',
  keywords = ['AI', 'agents', 'orchestration', 'cerebras', 'langchain', 'autogen', 'machine learning'],
  canonical,
  ogImage = '/og-image.png',
  noindex = false,
}: MetaTagsProps) {
  const fullTitle = title.includes('AI Orchestration Platform') 
    ? title 
    : `${title} | AI Orchestration Platform`;

  return (
    <Head>
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      
      {noindex && <meta name="robots" content="noindex,nofollow" />}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Additional SEO */}
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <meta name="theme-color" content="#3B82F6" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    </Head>
  );
}

// ===================================

// DEPLOYMENT_GUIDE.md
# 🚀 Production Deployment Guide

## Prerequisites Checklist

### Required Services
- [ ] **Supabase Account** - Database & Authentication
- [ ] **Cerebras AI API Key** - AI Model Access  
- [ ] **Stripe Account** - Payment Processing
- [ ] **Vercel Account** - Hosting (recommended)
- [ ] **Domain Name** - Custom domain (optional)

### Development Environment
- [ ] Node.js 18.17+ installed
- [ ] Git repository created
- [ ] Environment variables configured

## Step-by-Step Deployment

### 1. Supabase Setup

```bash
# 1. Create new Supabase project at https://supabase.com
# 2. Get your project URL and anon key
# 3. Run the database schema
```

In your Supabase SQL Editor, run:
```sql
-- Copy the entire contents of supabase_schema.sql
-- This creates all necessary tables and security policies
```

### 2. Stripe Configuration

```bash
# 1. Create Stripe account at https://stripe.com
# 2. Get your publishable and secret keys
# 3. Create products and prices for your subscription plans
```

Create products in Stripe Dashboard:
- **Starter Plan**: $29/month
- **Pro Plan**: $99/month  
- **Enterprise Plan**: $499/month

### 3. Environment Variables

Create production environment variables:

```env
# Cerebras AI
CEREBRAS_API_KEY=your_production_cerebras_key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe
STRIPE_SECRET_KEY=sk_live_your_live_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_SECRET=your_secure_random_secret
```

### 4. Vercel Deployment

#### Option A: One-Click Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/ai-orchestration-platform)

#### Option B: Manual Deploy
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Add environment variables in Vercel dashboard
```

### 5. Custom Domain (Optional)

```bash
# In Vercel dashboard:
# 1. Go to Project Settings → Domains
# 2. Add your custom domain
# 3. Update DNS records as instructed
```

### 6. Stripe Webhooks

```bash
# 1. In Stripe Dashboard → Webhooks
# 2. Add endpoint: https://your-domain.com/api/stripe/webhook
# 3. Select events:
#    - customer.subscription.created
#    - customer.subscription.updated
#    - customer.subscription.deleted
#    - invoice.payment_succeeded
#    - invoice.payment_failed
```

## Post-Deployment Configuration

### Security Checklist
- [ ] Enable Supabase RLS policies
- [ ] Configure CORS headers
- [ ] Set up SSL certificates (automatic with Vercel)
- [ ] Enable Stripe webhook signatures
- [ ] Configure rate limiting

### Monitoring Setup
- [ ] Set up error tracking (Sentry recommended)
- [ ] Configure analytics (PostHog, Mixpanel)
- [ ] Enable performance monitoring
- [ ] Set up uptime monitoring

### Business Configuration
- [ ] Configure email templates in Supabase
- [ ] Set up customer support system
- [ ] Create onboarding documentation
- [ ] Configure billing notifications

## Performance Optimization

### Caching Strategy
```javascript
// API routes automatically cached
// Static assets cached at edge
// Database queries optimized with indexes
```

### CDN Configuration
```javascript
// Images and assets served from Vercel Edge Network
// Global distribution for fast loading
```

### Database Optimization
```sql
-- Indexes already included in schema
-- Connection pooling enabled by default
-- Row Level Security for data isolation
```

## Scaling Considerations

### Traffic Growth
- **0-1K users**: Single Vercel deployment
- **1K-10K users**: Add Redis caching
- **10K+ users**: Consider database replicas

### Cost Management
- Monitor Cerebras API usage
- Set up billing alerts in Stripe
- Optimize database queries
- Use Vercel analytics for traffic insights

## Maintenance

### Regular Tasks
- [ ] Monitor error rates
- [ ] Review performance metrics
- [ ] Update dependencies monthly
- [ ] Backup database weekly
- [ ] Review security logs

### Updates
```bash
# Update dependencies
npm update

# Deploy updates
git push main  # Auto-deploys via Vercel
```

## Troubleshooting

### Common Issues
1. **Database Connection**: Check Supabase status
2. **API Errors**: Verify environment variables
3. **Payment Issues**: Check Stripe webhook logs
4. **Performance**: Review Vercel function logs

### Support Resources
- Vercel Documentation: https://vercel.com/docs
- Supabase Documentation: https://supabase.com/docs
- Stripe Documentation: https://stripe.com/docs

## Success Metrics

Track these KPIs post-deployment:
- User registration rate
- Subscription conversion rate
- API response times
- Error rates
- Customer satisfaction

---

**🎉 Congratulations!** Your AI Orchestration Platform is now live and ready to generate revenue!