# 🚀 Deployment Guide

This guide will help you deploy the AI Orchestration Platform to production.

## Prerequisites

Before deploying, ensure you have:

- [ ] Vercel account
- [ ] Supabase account
- [ ] Cerebras AI API key
- [ ] Stripe account
- [ ] Domain name (optional)

## Step-by-Step Deployment

### 1. Prepare Your Repository

1. Fork or clone the repository
2. Push your code to GitHub
3. Ensure all environment variables are documented

### 2. Set Up Supabase

1. **Create a new project** at [supabase.com](https://supabase.com)
2. **Run the database schema**:
   - Go to SQL Editor in Supabase dashboard
   - Copy and paste the contents of `supabase_schema.sql`
   - Execute the SQL

3. **Configure Authentication**:
   - Go to Authentication > Settings
   - Add your production domain to "Site URL"
   - Configure redirect URLs

4. **Get your credentials**:
   - Project URL: `https://your-project.supabase.co`
   - Anon key: Found in Settings > API
   - Service role key: Found in Settings > API (keep this secret!)

### 3. Set Up Stripe

1. **Create products and prices**:
   ```bash
   # Starter Plan
   stripe products create --name="Starter Plan" --description="25 agents, 10K tokens/month"
   stripe prices create --product=prod_xxx --unit-amount=2900 --currency=usd --recurring[interval]=month

   # Pro Plan  
   stripe products create --name="Pro Plan" --description="100 agents, 100K tokens/month"
   stripe prices create --product=prod_xxx --unit-amount=9900 --currency=usd --recurring[interval]=month

   # Enterprise Plan
   stripe products create --name="Enterprise Plan" --description="Unlimited agents, 1M tokens/month"
   stripe prices create --product=prod_xxx --unit-amount=49900 --currency=usd --recurring[interval]=month
   ```

2. **Configure webhook endpoint**:
   - URL: `https://your-domain.com/api/billing/webhook`
   - Events: `checkout.session.completed`, `customer.subscription.updated`, `customer.subscription.deleted`

3. **Get your credentials**:
   - Publishable key: `pk_live_...`
   - Secret key: `sk_live_...`
   - Webhook secret: `whsec_...`

### 4. Get Cerebras AI API Key

1. Sign up at [cerebras.ai](https://cerebras.ai)
2. Get your API key from the dashboard
3. Test the connection:
   ```bash
   curl -X POST https://api.cerebras.ai/v1/chat/completions \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "llama3.1-8b",
       "messages": [{"role": "user", "content": "Hello!"}],
       "max_tokens": 10
     }'
   ```

### 5. Deploy to Vercel

#### Option A: One-Click Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/ai-orchestration-platform)

#### Option B: Manual Deploy

1. **Install Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**:
   ```bash
   vercel login
   ```

3. **Deploy**:
   ```bash
   vercel --prod
   ```

#### Option C: GitHub Integration

1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your GitHub repository
4. Configure environment variables
5. Deploy

### 6. Configure Environment Variables

In your Vercel dashboard, add these environment variables:

```env
# Cerebras AI
CEREBRAS_API_KEY=your_cerebras_api_key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe
STRIPE_SECRET_KEY=sk_live_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_STARTER_PRICE_ID=price_...
STRIPE_PRO_PRICE_ID=price_...
STRIPE_ENTERPRISE_PRICE_ID=price_...

# App
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXTAUTH_SECRET=your_random_secret_32_chars_min
```

### 7. Post-Deployment Configuration

1. **Update Supabase Auth Settings**:
   - Add your production domain to allowed origins
   - Update redirect URLs

2. **Update Stripe Webhook**:
   - Set webhook URL to `https://your-domain.com/api/billing/webhook`
   - Test webhook delivery

3. **Test the Application**:
   - Sign up for a new account
   - Create an agent
   - Run an execution
   - Test subscription flow

## Custom Domain Setup

### 1. Add Domain to Vercel

1. Go to your project settings in Vercel
2. Click "Domains"
3. Add your custom domain
4. Follow DNS configuration instructions

### 2. Update Environment Variables

Update `NEXT_PUBLIC_APP_URL` to your custom domain:

```env
NEXT_PUBLIC_APP_URL=https://your-custom-domain.com
```

### 3. Update External Services

- **Supabase**: Add custom domain to auth settings
- **Stripe**: Update webhook URL to use custom domain

## Monitoring and Maintenance

### 1. Set Up Monitoring

- **Vercel Analytics**: Enable in project settings
- **Supabase Monitoring**: Monitor database performance
- **Stripe Dashboard**: Monitor payments and subscriptions

### 2. Regular Maintenance

- **Database Backups**: Supabase handles this automatically
- **Security Updates**: Keep dependencies updated
- **Performance Monitoring**: Monitor API response times

### 3. Scaling Considerations

- **Database**: Upgrade Supabase plan as needed
- **Vercel**: Pro plan for better performance
- **Stripe**: No scaling needed

## Troubleshooting

### Common Issues

1. **Environment Variables Not Loading**:
   - Redeploy after adding variables
   - Check variable names match exactly

2. **Database Connection Issues**:
   - Verify Supabase URL and keys
   - Check RLS policies are enabled

3. **Stripe Webhook Failures**:
   - Verify webhook URL is correct
   - Check webhook secret matches

4. **Build Failures**:
   - Run `npm run build` locally first
   - Check for TypeScript errors

### Getting Help

- Check Vercel deployment logs
- Review Supabase logs
- Test API endpoints individually
- Contact support if needed

## Security Checklist

- [ ] All environment variables are secure
- [ ] Supabase RLS policies are enabled
- [ ] Stripe webhook signature verification is working
- [ ] HTTPS is enforced
- [ ] API rate limiting is configured
- [ ] User input validation is in place

## Performance Optimization

- [ ] Enable Vercel Analytics
- [ ] Configure caching headers
- [ ] Optimize images and assets
- [ ] Monitor database query performance
- [ ] Set up CDN for static assets

---

🎉 **Congratulations!** Your AI Orchestration Platform is now live and ready to serve users worldwide!
